<template>
  <div class="company-management">
    <!-- 页面标题和操作按钮区域 -->
    <div class="company-header">
      <div class="company-title">
        <h2>公司管理</h2>
        <p class="description">管理您的公司信息，创建和维护公司档案</p>
      </div>
      
      <!-- 创建公司按钮 -->
      <a-button 
        type="primary" 
        size="large"
        @click="showCreateModal"
        :icon="h(PlusOutlined)"
      >
        创建公司
      </a-button>
    </div>

    <!-- 公司列表 -->
    <a-card class="company-list-card" :bordered="false">
      <template #title>
        <div class="list-header">
          <span>我的公司</span>
          <!-- 搜索框 -->
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索公司名称"
            style="width: 300px"
            @search="loadCompanyList"
            allow-clear
          />
        </div>
      </template>

      <!-- 公司表格 -->
      <div v-if="companyList.length === 0 && !listLoading" class="empty-state">
        <a-empty description="暂无公司数据">
          <a-button type="primary" @click="showCreateModal">
            立即创建公司
          </a-button>
        </a-empty>
      </div>

      <a-table
        v-else
        :columns="tableColumns"
        :data-source="companyList"
        :loading="listLoading"
        :pagination="paginationConfig"
        row-key="公司id"
        @change="handleTableChange"
        :scroll="{ x: 800 }"
      >
        <template #bodyCell="{ column, record }">
          <!-- 公司名称列 -->
          <template v-if="column.key === '公司名称'">
            <div class="company-name-cell">
              <div class="company-name">{{ record.公司名称 }}</div>
              <a-tag v-if="record.公司简称" color="blue" size="small">
                {{ record.公司简称 }}
              </a-tag>
            </div>
          </template>

          <!-- 审核状态列 -->
          <template v-else-if="column.key === '审核状态'">
            <a-tag
              :color="getStatusColor(record.审核状态)"
              :icon="h(getStatusIcon(record.审核状态))"
            >
              {{ getStatusText(record.审核状态) }}
            </a-tag>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === '创建时间'">
            {{ formatDate(record.创建时间) }}
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <!-- 统一详情按钮 -->
              <a-button size="small" @click="openCompanyDetail(record)">
                详情
              </a-button>

              <!-- 根据审核状态显示不同操作 -->
              <template v-if="record.审核状态 === 0">
                <!-- 未审核：可以编辑 -->
                <a-button size="small" type="primary" @click="openCompanyDetail(record, 'edit')">
                  编辑
                </a-button>
              </template>
              <template v-else-if="record.审核状态 === 2">
                <!-- 审核不通过：可以重新提交 -->
                <a-button size="small" type="primary" @click="resubmitCompany(record)">
                  重新提交
                </a-button>
              </template>
              <template v-else-if="record.审核状态 === 1">
                <!-- 审核通过：只能查看 -->
                <a-tag color="green" size="small">已通过</a-tag>
              </template>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 分页器 -->
      <div v-if="total > 0" class="pagination-wrapper">
        <a-pagination
          v-model:current="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          show-size-changer
          show-quick-jumper
          :show-total="(total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </a-card>

    <!-- 创建/编辑公司模态框 -->
    <a-modal
      v-model:open="createModalVisible"
      title="创建公司"
      width="600px"
      :confirm-loading="createLoading"
      @ok="handleSubmitCompany"
      @cancel="handleCancelCreate"
    >
      <!-- 公司信息表单 -->
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        layout="vertical"
        class="company-form"
      >
        <!-- 基本信息区域 -->
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          
          <!-- 公司名称 - 必填字段 -->
          <a-form-item label="公司名称" name="公司名称" class="required-field">
            <a-input
              v-model:value="createForm.公司名称"
              placeholder="请输入公司全称"
              :maxlength="255"
              show-count
            />
          </a-form-item>
          
          <!-- 公司简称 - 可选字段，用于显示标签 -->
          <a-form-item label="公司简称" name="公司简称">
            <a-input
              v-model:value="createForm.公司简称"
              placeholder="请输入公司简称（可选）"
              :maxlength="100"
              show-count
            />
          </a-form-item>
          
          <!-- 公司代码 - 用于系统内部标识 -->
          <a-form-item label="公司代码" name="公司代码">
            <a-input
              v-model:value="createForm.公司代码"
              placeholder="请输入公司代码（可选）"
              :maxlength="50"
              show-count
            />
          </a-form-item>
        </div>

        <!-- 联系信息区域 -->
        <div class="form-section">
          <h4 class="section-title">联系信息</h4>
          
          <!-- 公司地址 -->
          <a-form-item label="公司地址" name="公司地址">
            <a-textarea
              v-model:value="createForm.公司地址"
              placeholder="请输入公司详细地址"
              :rows="2"
              :maxlength="500"
              show-count
            />
          </a-form-item>
          
          <!-- 联系电话 -->
          <a-form-item label="联系电话" name="联系电话">
            <a-input
              v-model:value="createForm.联系电话"
              placeholder="请输入公司联系电话"
              :maxlength="50"
            />
          </a-form-item>
          
          <!-- 公司邮箱 -->
          <a-form-item label="公司邮箱" name="邮箱">
            <a-input
              v-model:value="createForm.邮箱"
              placeholder="请输入公司邮箱地址"
              :maxlength="255"
            />
          </a-form-item>
        </div>

        <!-- 法务信息区域 -->
        <div class="form-section">
          <h4 class="section-title">法务信息</h4>
          
          <!-- 法人代表 -->
          <a-form-item label="法人代表" name="法人代表">
            <a-input
              v-model:value="createForm.法人代表"
              placeholder="请输入法人代表姓名"
              :maxlength="100"
            />
          </a-form-item>
          
          <!-- 营业执照号 -->
          <a-form-item label="营业执照号" name="营业执照号">
            <a-input
              v-model:value="createForm.营业执照号"
              placeholder="请输入营业执照号"
              :maxlength="100"
            />
          </a-form-item>
        </div>

        <!-- 其他信息区域 -->
        <div class="form-section">
          <h4 class="section-title">其他信息</h4>
          
          <!-- 备注信息 -->
          <a-form-item label="备注" name="备注">
            <a-textarea
              v-model:value="createForm.备注"
              placeholder="请输入备注信息（可选）"
              :rows="3"
              :maxlength="1000"
              show-count
            />
          </a-form-item>
        </div>
      </a-form>
    </a-modal>

    <!-- 统一的公司详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      :title="detailModalTitle"
      width="800px"
      :confirm-loading="detailLoading"
      @ok="handleDetailSubmit"
      @cancel="handleDetailCancel"
    >
      <div v-if="selectedCompany" class="company-detail">
        <!-- 模式切换按钮 -->
        <div class="detail-header">
          <a-space>
            <a-button
              :type="detailMode === 'view' ? 'primary' : 'default'"
              @click="switchDetailMode('view')"
              size="small"
            >
              查看模式
            </a-button>
            <a-button
              v-if="canEdit"
              :type="detailMode === 'edit' ? 'primary' : 'default'"
              @click="switchDetailMode('edit')"
              size="small"
            >
              编辑模式
            </a-button>
          </a-space>

          <!-- 审核状态显示 -->
          <div class="status-display">
            <a-tag :color="getStatusColor(selectedCompany.审核状态)" :icon="h(getStatusIcon(selectedCompany.审核状态))">
              {{ getStatusText(selectedCompany.审核状态) }}
            </a-tag>
            <!-- 重新提交提示 -->
            <a-tag v-if="isResubmitting" color="blue" class="resubmit-tag">
              重新提交审核
            </a-tag>
          </div>
        </div>

        <!-- 查看模式 -->
        <div v-if="detailMode === 'view'" class="view-mode">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="公司名称">
              {{ selectedCompany.公司名称 }}
            </a-descriptions-item>
            <a-descriptions-item label="公司简称">
              {{ selectedCompany.公司简称 || '未设置' }}
            </a-descriptions-item>
            <a-descriptions-item label="公司代码">
              {{ selectedCompany.公司代码 || '未设置' }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(selectedCompany.创建时间) }}
            </a-descriptions-item>
            <a-descriptions-item label="公司地址" :span="2">
              {{ selectedCompany.公司地址 || '未填写' }}
            </a-descriptions-item>
            <a-descriptions-item label="联系电话">
              {{ selectedCompany.联系电话 || '未填写' }}
            </a-descriptions-item>
            <a-descriptions-item label="公司邮箱">
              {{ selectedCompany.邮箱 || '未填写' }}
            </a-descriptions-item>
            <a-descriptions-item label="法人代表">
              {{ selectedCompany.法人代表 || '未填写' }}
            </a-descriptions-item>
            <a-descriptions-item label="营业执照号">
              {{ selectedCompany.营业执照号 || '未填写' }}
            </a-descriptions-item>
            <a-descriptions-item v-if="selectedCompany.备注" label="备注" :span="2">
              {{ selectedCompany.备注 }}
            </a-descriptions-item>
          </a-descriptions>

          <!-- 审核历史记录 -->
          <div v-if="selectedCompany.审核状态 !== 0" class="audit-history">
            <h4>审核记录</h4>
            <a-timeline>
              <a-timeline-item
                :color="selectedCompany.审核状态 === 1 ? 'green' : 'red'"
                :dot="h(selectedCompany.审核状态 === 1 ? CheckCircleOutlined : CloseCircleOutlined)"
              >
                <p>{{ selectedCompany.审核状态 === 1 ? '审核通过' : '审核不通过' }}</p>
                <p class="audit-time">{{ formatDate(selectedCompany.审核时间) }}</p>
                <p v-if="selectedCompany.审核备注" class="audit-remark">{{ selectedCompany.审核备注 }}</p>
              </a-timeline-item>
              <a-timeline-item color="blue" :dot="h(ClockCircleOutlined)">
                <p>提交审核</p>
                <p class="audit-time">{{ formatDate(selectedCompany.创建时间) }}</p>
              </a-timeline-item>
            </a-timeline>
          </div>
        </div>

        <!-- 编辑模式 -->
        <div v-else-if="detailMode === 'edit'" class="edit-mode">
          <a-form
            ref="detailFormRef"
            :model="detailForm"
            :rules="createFormRules"
            layout="vertical"
            class="company-form"
          >
            <!-- 基本信息区域 -->
            <div class="form-section">
              <h4 class="section-title">基本信息</h4>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="公司名称" name="公司名称" class="required-field">
                    <a-input
                      v-model:value="detailForm.公司名称"
                      placeholder="请输入公司全称"
                      :maxlength="255"
                      show-count
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="公司简称" name="公司简称">
                    <a-input
                      v-model:value="detailForm.公司简称"
                      placeholder="请输入公司简称（可选）"
                      :maxlength="100"
                      show-count
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="公司代码" name="公司代码">
                    <a-input
                      v-model:value="detailForm.公司代码"
                      placeholder="请输入公司代码（可选）"
                      :maxlength="50"
                      show-count
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="联系电话" name="联系电话">
                    <a-input
                      v-model:value="detailForm.联系电话"
                      placeholder="请输入联系电话"
                      :maxlength="50"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="公司地址" name="公司地址">
                <a-input
                  v-model:value="detailForm.公司地址"
                  placeholder="请输入公司地址"
                />
              </a-form-item>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="公司邮箱" name="邮箱">
                    <a-input
                      v-model:value="detailForm.邮箱"
                      placeholder="请输入公司邮箱"
                      :maxlength="255"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="法人代表" name="法人代表">
                    <a-input
                      v-model:value="detailForm.法人代表"
                      placeholder="请输入法人代表"
                      :maxlength="100"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="营业执照号" name="营业执照号">
                <a-input
                  v-model:value="detailForm.营业执照号"
                  placeholder="请输入营业执照号"
                  :maxlength="100"
                />
              </a-form-item>

              <a-form-item label="备注" name="备注">
                <a-textarea
                  v-model:value="detailForm.备注"
                  placeholder="请输入备注信息（可选）"
                  :rows="3"
                  :maxlength="500"
                  show-count
                />
              </a-form-item>
            </div>
          </a-form>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import teamService from '../../services/team'
import { teamBasicService } from '../../services/team/teamBasic'
import { COMPANY_STATUS, getAuditStatusText, getAuditStatusColor } from '../../constants/businessStatus'

/**
 * 公司管理组件
 * 
 * 功能说明：
 * 1. 显示用户创建的公司列表
 * 2. 提供创建新公司的功能
 * 3. 支持编辑现有公司信息
 * 4. 查看公司详细信息
 * 5. 搜索和分页功能
 * 
 * 设计原则：
 * - 用户友好的界面设计
 * - 详细的表单验证
 * - 完善的错误处理
 * - 响应式布局适配
 */
defineOptions({
  name: 'CompanyManagement'
})

// ================== 响应式数据定义 ==================

/**
 * 公司列表相关数据
 * 用于管理公司列表的显示和操作
 */
const companyList = ref([])           // 公司列表数据
const listLoading = ref(false)        // 列表加载状态
const searchKeyword = ref('')         // 搜索关键词
const total = ref(0)                  // 总记录数
const currentPage = ref(1)            // 当前页码
const pageSize = ref(10)              // 每页显示数量

/**
 * 创建公司模态框相关数据
 */
const createModalVisible = ref(false) // 创建模态框显示状态
const createLoading = ref(false)      // 提交按钮加载状态
const createFormRef = ref()           // 表单引用

/**
 * 统一的公司详情模态框相关数据
 */
const detailModalVisible = ref(false) // 详情模态框显示状态
const selectedCompany = ref(null)     // 选中的公司数据
const detailMode = ref('view')        // 详情模式：'view' | 'edit'
const detailLoading = ref(false)      // 详情操作加载状态
const detailFormRef = ref()           // 详情表单引用
const isResubmitting = ref(false)     // 是否为重新提交模式

/**
 * 详情表单数据
 */
const detailForm = reactive({
  公司id: '',
  公司名称: '',
  公司简称: '',
  公司代码: '',
  公司地址: '',
  联系电话: '',
  邮箱: '',
  法人代表: '',
  营业执照号: '',
  备注: ''
})

/**
 * 表格列配置
 */
const tableColumns = [
  {
    title: '公司名称',
    key: '公司名称',
    dataIndex: '公司名称',
    width: 250,
    fixed: 'left'
  },
  {
    title: '审核状态',
    key: '审核状态',
    dataIndex: '审核状态',
    width: 150,
    align: 'center'
  },
  {
    title: '创建时间',
    key: '创建时间',
    dataIndex: '创建时间',
    width: 180,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
    align: 'center'
  }
]

/**
 * 分页配置
 */
const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: pageSize.value,
  total: total.value,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  pageSizeOptions: ['10', '20', '50', '100']
}))

/**
 * 创建公司表单数据
 * 包含公司创建和编辑所需的所有字段
 */
const createForm = reactive({
  公司名称: '',        // 必填字段
  公司简称: '',        // 可选字段，用于显示
  公司代码: '',        // 可选字段，系统标识
  公司地址: '',        // 可选字段，公司地址
  联系电话: '',        // 可选字段，联系方式
  邮箱: '',           // 可选字段，邮箱地址
  法人代表: '',        // 可选字段，法务信息
  营业执照号: '',      // 可选字段，法务信息
  备注: ''            // 可选字段，其他说明
})

/**
 * 表单验证规则
 * 定义各字段的验证要求，确保数据质量
 */
const createFormRules = {
  公司名称: [
    { required: true, message: '请输入公司名称', trigger: 'blur' },
    { min: 1, max: 255, message: '公司名称长度在1到255个字符', trigger: 'blur' }
  ],
  公司简称: [
    { max: 100, message: '公司简称不能超过100个字符', trigger: 'blur' }
  ],
  公司代码: [
    { max: 50, message: '公司代码不能超过50个字符', trigger: 'blur' }
  ],
  联系电话: [
    { pattern: /^[\d\-\+\(\)\s]+$/, message: '请输入有效的电话号码', trigger: 'blur' }
  ],
  邮箱: [
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  营业执照号: [
    { max: 100, message: '营业执照号不能超过100个字符', trigger: 'blur' }
  ]
}

// ================== 计算属性 ==================

/**
 * 详情模态框标题
 */
const detailModalTitle = computed(() => {
  if (!selectedCompany.value) return '公司详情'

  let modeName = '查看'
  if (detailMode.value === 'edit') {
    modeName = isResubmitting.value ? '重新提交' : '编辑'
  }

  return `${modeName}公司 - ${selectedCompany.value.公司名称}`
})

/**
 * 是否可以编辑
 */
const canEdit = computed(() => {
  if (!selectedCompany.value) return false
  // 只有未审核(0)和审核不通过(2)的公司可以编辑
  return selectedCompany.value.审核状态 === 0 || selectedCompany.value.审核状态 === 2
})

// ================== 业务逻辑方法 ==================

/**
 * 处理表格变化（分页、排序、筛选）
 */
const handleTableChange = (pagination, filters, sorter) => {
  currentPage.value = pagination.current
  pageSize.value = pagination.pageSize
  loadCompanyList()
}

/**
 * 加载公司列表
 *
 * 功能说明：
 * 1. 调用后端API获取公司列表
 * 2. 支持分页和搜索功能
 * 3. 处理加载状态和错误情况
 *
 * @param {boolean} resetPage - 是否重置页码到第一页
 */
const loadCompanyList = async (resetPage = false) => {
  try {
    // 如果需要重置页码（如搜索时）
    if (resetPage) {
      currentPage.value = 1
    }

    listLoading.value = true

    // 调用后端API获取公司列表
    const response = await teamService.getCompanyList({
      页码: currentPage.value,
      每页数量: pageSize.value,
      搜索关键词: searchKeyword.value || undefined
    })

    // 处理API响应
    if (response.status === 100 && response.data) {
      companyList.value = response.data.公司列表 || []
      total.value = response.data.总数 || 0
    } else {
      // API调用失败，显示错误信息
      message.error('获取公司列表失败：' + (response.message || '未知错误'))
      companyList.value = []
      total.value = 0
    }
  } catch (error) {
    // 网络或其他异常错误
    console.error('加载公司列表失败:', error)
    message.error('加载公司列表失败：' + error.message)
    companyList.value = []
    total.value = 0
  } finally {
    listLoading.value = false
  }
}

/**
 * 显示创建公司模态框
 * 
 * 功能说明：
 * 1. 重置表单数据
 * 2. 设置为创建模式
 * 3. 显示模态框
 */
const showCreateModal = () => {
  resetCreateForm()
  createModalVisible.value = true
}

/**
 * 打开公司详情模态框
 *
 * @param {Object} company - 公司数据
 * @param {string} mode - 模式：'view' | 'edit'
 * @param {boolean} resubmit - 是否为重新提交模式
 */
const openCompanyDetail = (company, mode = 'view', resubmit = false) => {
  selectedCompany.value = company
  detailMode.value = mode
  isResubmitting.value = resubmit

  // 如果是编辑模式，填充表单数据
  if (mode === 'edit') {
    fillForm(detailForm, company)
  }

  detailModalVisible.value = true
}

/**
 * 切换详情模式
 *
 * @param {string} mode - 模式：'view' | 'edit'
 */
const switchDetailMode = (mode) => {
  if (mode === 'edit' && !canEdit.value) {
    message.warning('当前状态下不允许编辑')
    return
  }

  detailMode.value = mode

  // 切换到编辑模式时，填充表单数据
  if (mode === 'edit' && selectedCompany.value) {
    fillForm(detailForm, selectedCompany.value)
  }
}

/**
 * 重新提交公司申请
 *
 * @param {Object} company - 公司数据
 */
const resubmitCompany = (company) => {
  openCompanyDetail(company, 'edit', true) // 传递重新提交标识
  message.info('请修改公司信息后重新提交审核')
}

/**
 * 处理详情模态框提交
 */
const handleDetailSubmit = async () => {
  if (detailMode.value === 'view') {
    detailModalVisible.value = false
    return
  }

  try {
    // 表单验证
    await detailFormRef.value.validate()

    detailLoading.value = true

    // 调用更新API
    const response = await teamBasicService.updateCompany(detailForm)

    // 根据操作类型显示不同的成功消息
    const successMessage = isResubmitting.value
      ? '公司信息更新成功，已重新提交审核'
      : '公司信息更新成功'

    // 使用统一的业务响应处理方法
    await handleBusinessResponse(
      response,
      successMessage,
      async () => {
        detailModalVisible.value = false
        isResubmitting.value = false // 重置重新提交状态
        await loadCompanyList()
      }
    )
  } catch (error) {
    if (error.errorFields) {
      return // 表单验证错误，不显示消息
    }
    console.error('更新公司信息失败:', error)
    message.error('更新公司信息失败：' + error.message)
  } finally {
    detailLoading.value = false
  }
}

/**
 * 处理详情模态框取消
 */
const handleDetailCancel = () => {
  detailModalVisible.value = false
  detailMode.value = 'view'
  isResubmitting.value = false
  resetForm(detailForm, detailFormRef)
}

/**
 * 提交公司创建/编辑表单
 * 
 * 功能说明：
 * 1. 表单验证
 * 2. 调用相应的API（创建或编辑）
 * 3. 处理成功和失败情况
 */
const handleSubmitCompany = async () => {
  try {
    // 表单验证
    await createFormRef.value.validate()
    
    createLoading.value = true
    
    // 调用创建API
    const response = await teamService.createCompany(createForm)

    // 使用统一的业务响应处理方法
    await handleBusinessResponse(
      response,
      '公司创建成功',
      async () => {
        createModalVisible.value = false
        resetCreateForm()
        await loadCompanyList()
      }
    )
  } catch (error) {
    // 如果是表单验证错误，不显示消息
    if (error.errorFields) {
      return
    }
    
    // 其他错误
    console.error('提交公司信息失败:', error)
    message.error('操作失败：' + error.message)
  } finally {
    createLoading.value = false
  }
}

/**
 * 取消创建/编辑操作
 * 
 * 功能说明：
 * 1. 关闭模态框
 * 2. 重置表单数据
 */
const handleCancelCreate = () => {
  createModalVisible.value = false
  resetCreateForm()
}

/**
 * 重置创建表单
 */
const resetCreateForm = () => resetForm(createForm, createFormRef)

/**
 * 处理页码变化
 * 
 * @param {number} page - 新的页码
 * @param {number} size - 每页大小
 */
const handlePageChange = (page, size) => {
  currentPage.value = page
  pageSize.value = size
  loadCompanyList()
}

/**
 * 处理每页大小变化
 * 
 * @param {number} current - 当前页码
 * @param {number} size - 新的每页大小
 */
const handlePageSizeChange = (current, size) => {
  currentPage.value = 1  // 重置到第一页
  pageSize.value = size
  loadCompanyList()
}

// ================== 工具方法 ==================



/**
 * 通用表单重置方法
 * @param {Object} formData - 表单数据对象
 * @param {Object} formRef - 表单引用
 */
const resetForm = (formData, formRef = null) => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })

  // 清除表单验证状态
  if (formRef?.value) {
    formRef.value.clearValidate()
  }
}

/**
 * 通用表单数据填充方法
 * @param {Object} targetForm - 目标表单对象
 * @param {Object} sourceData - 源数据对象
 */
const fillForm = (targetForm, sourceData) => {
  Object.keys(targetForm).forEach(key => {
    targetForm[key] = sourceData[key] || ''
  })
}

/**
 * 处理业务响应的统一方法
 * @param {Object} response - API响应
 * @param {string} successMessage - 成功消息
 * @param {Function} onSuccess - 成功回调
 */
const handleBusinessResponse = async (response, successMessage, onSuccess) => {
  if (response && response.status === 100) {
    message.success(successMessage)
    if (onSuccess) await onSuccess()
  } else {
    const errorMessage = response?.message || '操作失败'
    const statusCode = response?.status

    if (statusCode === COMPANY_STATUS.PENDING_REVIEW) {
      message.warning({
        content: errorMessage,
        duration: 6
      })
    } else if (statusCode === COMPANY_STATUS.NAME_EXISTS ||
               statusCode === COMPANY_STATUS.CODE_EXISTS) {
      message.error({
        content: errorMessage,
        duration: 4
      })
    } else {
      message.error(errorMessage)
    }
  }
}

/**
 * 格式化日期显示
 *
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 使用导入的状态处理函数
const getStatusText = getAuditStatusText
const getStatusColor = getAuditStatusColor

/**
 * 获取审核状态对应的图标
 *
 * @param {number} status - 审核状态（0=未审核，1=审核通过，2=审核不通过）
 * @returns {Object} 对应的图标组件
 */
const getStatusIcon = (status) => {
  const iconMap = {
    0: ClockCircleOutlined,
    1: CheckCircleOutlined,
    2: CloseCircleOutlined
  }
  return iconMap[status] || ClockCircleOutlined
}

// ================== 生命周期钩子 ==================

/**
 * 组件挂载后的初始化操作
 * 
 * 功能说明：
 * 1. 加载公司列表数据
 * 2. 初始化组件状态
 */
onMounted(() => {
  loadCompanyList()
})

/**
 * 向父组件发送保存事件
 * 这是为了与系统设置页面的统一保存机制集成
 */
const emit = defineEmits(['save'])

/**
 * 模拟保存操作，与系统设置页面集成
 */
const handleSave = () => {
  emit('save', 'company', {
    message: '公司管理设置已更新'
  })
}
</script>

<style scoped>
/* ================== 组件整体样式 ================== */
.company-management {
  padding: 0;
  max-width: 100%;
}

/* ================== 页面头部样式 ================== */
.company-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
}

.company-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.company-title .description {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

/* ================== 列表卡片样式 ================== */
.company-list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.list-header span {
  font-size: 16px;
  font-weight: 500;
}

/* ================== 空状态样式 ================== */
.empty-state {
  padding: 48px 24px;
  text-align: center;
}

/* ================== 空状态样式 ================== */
.empty-state {
  padding: 48px 24px;
  text-align: center;
}

/* ================== 表格相关样式 ================== */
.company-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.company-name {
  font-weight: 600;
  color: #262626;
}

/* ================== 分页器样式 ================== */
.pagination-wrapper {
  margin-top: 24px;
  text-align: center;
}

/* ================== 表单样式 ================== */
.company-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 8px;
}

.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-left: 3px solid #1890ff;
  padding-left: 12px;
}

.required-field :deep(.ant-form-item-label) {
  font-weight: 600;
}

/* ================== 公司详情样式 ================== */
.company-detail {
  max-height: 60vh;
  overflow-y: auto;
}

/* 详情头部样式 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resubmit-tag {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

/* 查看模式样式 */
.view-mode {
  line-height: 1.6;
}

/* 审核历史样式 */
.audit-history {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.audit-history h4 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.audit-time {
  color: #8c8c8c;
  font-size: 12px;
  margin: 4px 0;
}

.audit-remark {
  color: #595959;
  font-size: 13px;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
}

/* 编辑模式样式 */
.edit-mode {
  margin-top: 16px;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

/* ================== 响应式设计 ================== */
@media (max-width: 768px) {
  .company-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .list-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

@media (max-width: 576px) {
  .company-management {
    padding: 0 8px;
  }
}
</style>