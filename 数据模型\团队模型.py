from datetime import datetime
from enum import Enum
from typing import Optional, List

from pydantic import BaseModel, Field


# =============== 枚举定义 ===============

class 公司状态枚举(str, Enum):
    """公司状态枚举定义"""
    正常 = "正常"
    停用 = "停用"
    注销 = "注销"

class 审核状态枚举(int, Enum):
    """审核状态枚举定义"""
    未审核 = 0
    审核通过 = 1
    审核不通过 = 2

class 团队状态枚举(str, Enum):
    """团队状态枚举定义"""
    正常 = "正常"
    停用 = "停用"
    解散 = "解散"

class 成员状态枚举(str, Enum):
    """成员状态枚举定义"""
    正常 = "正常"
    暂停 = "暂停"
    离职 = "离职"

class 权限状态枚举(str, Enum):
    """权限状态枚举定义"""
    启用 = "启用"
    禁用 = "禁用"

class 用户权限状态枚举(str, Enum):
    """用户权限状态枚举定义"""
    生效 = "生效"
    失效 = "失效"
    暂停 = "暂停"

# =============== 公司相关模型 ===============

class 公司基础模型(BaseModel):
    """公司基础信息模型"""
    id: Optional[int] = Field(None, description="公司id")
    公司名称: str = Field(..., description="公司名称")
    公司简称: Optional[str] = Field(None, description="公司简称")
    公司代码: Optional[str] = Field(None, description="公司代码")
    公司地址: Optional[str] = Field(None, description="公司地址")
    联系电话: Optional[str] = Field(None, description="联系电话")
    邮箱: Optional[str] = Field(None, description="公司邮箱")
    法人代表: Optional[str] = Field(None, description="法人代表")
    营业执照号: Optional[str] = Field(None, description="营业执照号")
    公司状态: 公司状态枚举 = Field(公司状态枚举.正常, description="公司状态")
    审核状态: 审核状态枚举 = Field(审核状态枚举.未审核, description="审核状态")
    审核人ID: Optional[int] = Field(None, description="审核人ID")
    审核时间: Optional[datetime] = Field(None, description="审核时间")
    审核备注: Optional[str] = Field(None, description="审核备注")
    创建人id: Optional[int] = Field(None, description="创建人id")
    创建时间: Optional[datetime] = Field(None, description="创建时间")
    更新时间: Optional[datetime] = Field(None, description="更新时间")
    备注: Optional[str] = Field(None, description="备注信息")

class 公司详情模型(公司基础模型):
    """公司详情模型，包含创建人信息"""
    创建人姓名: Optional[str] = Field(None, description="创建人姓名")
    审核人姓名: Optional[str] = Field(None, description="审核人姓名")
    团队数量: Optional[int] = Field(None, description="团队数量")
    员工数量: Optional[int] = Field(None, description="员工数量")

# =============== 团队相关模型 ===============

class 团队基础模型(BaseModel):
    """团队基础信息模型"""
    id: Optional[int] = Field(None, description="团队id")
    团队名称: str = Field(..., description="团队名称")
    团队代码: Optional[str] = Field(None, description="团队代码")
    团队描述: Optional[str] = Field(None, description="团队描述")
    公司id: int = Field(..., description="所属公司id")
    团队负责人id: Optional[int] = Field(None, description="团队负责人id")
    团队状态: 团队状态枚举 = Field(团队状态枚举.正常, description="团队状态")
    最大成员数: int = Field(100, description="最大成员数限制")
    当前成员数: int = Field(0, description="当前成员数")
    创建人id: Optional[int] = Field(None, description="创建人id")
    创建时间: Optional[datetime] = Field(None, description="创建时间")
    更新时间: Optional[datetime] = Field(None, description="更新时间")
    备注: Optional[str] = Field(None, description="备注信息")

class 团队详情模型(团队基础模型):
    """团队详情模型，包含关联信息"""
    公司名称: Optional[str] = Field(None, description="公司名称")
    公司简称: Optional[str] = Field(None, description="公司简称")
    团队负责人姓名: Optional[str] = Field(None, description="团队负责人姓名")
    创建人姓名: Optional[str] = Field(None, description="创建人姓名")

class 用户团队关联模型(BaseModel):
    """用户团队关联模型"""
    id: Optional[int] = Field(None, description="关联id")
    用户id: int = Field(..., description="用户id")
    团队id: int = Field(..., description="团队id")
    职位: Optional[str] = Field(None, description="在团队中的职位")
    加入时间: Optional[datetime] = Field(None, description="加入团队时间")
    离开时间: Optional[datetime] = Field(None, description="离开团队时间")
    状态: 成员状态枚举 = Field(成员状态枚举.正常, description="成员状态")
    邀请人id: Optional[int] = Field(None, description="邀请人id")
    审批人ID: Optional[int] = Field(None, description="审批人ID")
    审批时间: Optional[datetime] = Field(None, description="审批时间")
    备注: Optional[str] = Field(None, description="备注信息")
    创建时间: Optional[datetime] = Field(None, description="创建时间")
    更新时间: Optional[datetime] = Field(None, description="更新时间")

class 团队成员详情模型(用户团队关联模型):
    """团队成员详情模型，包含用户信息"""
    用户姓名: Optional[str] = Field(None, description="用户姓名")
    用户手机: Optional[str] = Field(None, description="用户手机")
    用户邮箱: Optional[str] = Field(None, description="用户邮箱")
    邀请人姓名: Optional[str] = Field(None, description="邀请人姓名")
    审批人姓名: Optional[str] = Field(None, description="审批人姓名")

# =============== 权限相关模型 ===============

class 团队权限模型(BaseModel):
    """团队权限模型"""
    id: Optional[int] = Field(None, description="权限ID")
    权限代码: str = Field(..., description="权限代码")
    权限名称: str = Field(..., description="权限名称")
    权限描述: Optional[str] = Field(None, description="权限描述")
    权限分类: Optional[str] = Field(None, description="权限分类")
    是否系统权限: bool = Field(False, description="是否为系统权限")
    排序: int = Field(0, description="显示排序")
    状态: 权限状态枚举 = Field(权限状态枚举.启用, description="权限状态")
    创建时间: Optional[datetime] = Field(None, description="创建时间")
    更新时间: Optional[datetime] = Field(None, description="更新时间")

class 用户团队权限模型(BaseModel):
    """用户团队权限模型"""
    id: Optional[int] = Field(None, description="用户权限ID")
    用户id: int = Field(..., description="用户id")
    团队id: int = Field(..., description="团队id")
    权限ID: int = Field(..., description="权限ID")
    授权人ID: Optional[int] = Field(None, description="授权人ID")
    授权时间: Optional[datetime] = Field(None, description="授权时间")
    过期时间: Optional[datetime] = Field(None, description="权限过期时间")
    状态: 用户权限状态枚举 = Field(用户权限状态枚举.生效, description="权限状态")
    备注: Optional[str] = Field(None, description="备注信息")
    创建时间: Optional[datetime] = Field(None, description="创建时间")
    更新时间: Optional[datetime] = Field(None, description="更新时间")

class 用户团队权限详情模型(用户团队权限模型):
    """用户团队权限详情模型，包含权限信息"""
    权限代码: Optional[str] = Field(None, description="权限代码")
    权限名称: Optional[str] = Field(None, description="权限名称")
    权限描述: Optional[str] = Field(None, description="权限描述")
    权限分类: Optional[str] = Field(None, description="权限分类")
    授权人姓名: Optional[str] = Field(None, description="授权人姓名")

# =============== 操作日志模型 ===============

class 团队操作日志模型(BaseModel):
    """团队操作日志模型"""
    id: Optional[int] = Field(None, description="日志ID")
    团队id: int = Field(..., description="团队id")
    操作人ID: Optional[int] = Field(None, description="操作人ID")
    操作类型: str = Field(..., description="操作类型")
    操作内容: Optional[str] = Field(None, description="操作内容详情")
    操作对象ID: Optional[int] = Field(None, description="操作对象ID")
    操作前数据: Optional[dict] = Field(None, description="操作前的数据快照")
    操作后数据: Optional[dict] = Field(None, description="操作后的数据快照")
    IP地址: Optional[str] = Field(None, description="操作IP地址")
    用户代理: Optional[str] = Field(None, description="用户代理信息")
    操作时间: Optional[datetime] = Field(None, description="操作时间")

class 团队操作日志详情模型(团队操作日志模型):
    """团队操作日志详情模型，包含操作人信息"""
    操作人姓名: Optional[str] = Field(None, description="操作人姓名")
    团队名称: Optional[str] = Field(None, description="团队名称")

# =============== 响应模型 ===============

class 分页响应基础模型(BaseModel):
    """分页响应基础模型"""
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: Optional[int] = Field(None, description="总页数")

class 公司列表响应模型(分页响应基础模型):
    """公司列表响应模型"""
    list: List[公司详情模型] = Field(..., description="公司列表")

class 团队列表响应模型(分页响应基础模型):
    """团队列表响应模型"""
    list: List[团队详情模型] = Field(..., description="团队列表")

class 团队成员列表响应模型(分页响应基础模型):
    """团队成员列表响应模型"""
    list: List[团队成员详情模型] = Field(..., description="团队成员列表")

class 权限列表响应模型(BaseModel):
    """权限列表响应模型"""
    list: List[团队权限模型] = Field(..., description="权限列表")

class 用户权限列表响应模型(BaseModel):
    """用户权限列表响应模型"""
    list: List[用户团队权限详情模型] = Field(..., description="用户权限列表")

# =============== 审核相关模型 ===============

class 公司审核请求模型(BaseModel):
    """公司审核请求模型"""
    公司id: int = Field(..., description="公司id")
    审核备注: Optional[str] = Field(None, description="审核备注")

class 公司审核响应模型(BaseModel):
    """公司审核响应模型"""
    公司id: int = Field(..., description="公司id")
    公司名称: str = Field(..., description="公司名称")
    审核状态: 审核状态枚举 = Field(..., description="审核状态")
    审核人ID: Optional[int] = Field(None, description="审核人ID")
    审核人姓名: Optional[str] = Field(None, description="审核人姓名")
    审核时间: Optional[datetime] = Field(None, description="审核时间")
    审核备注: Optional[str] = Field(None, description="审核备注")

class 待审核公司列表响应模型(分页响应基础模型):
    """待审核公司列表响应模型"""
    list: List[公司详情模型] = Field(..., description="待审核公司列表")

class 已审核公司列表响应模型(分页响应基础模型):
    """已审核公司列表响应模型"""
    list: List[公司详情模型] = Field(..., description="已审核公司列表")

# =============== 统计模型 ===============

class 团队统计模型(BaseModel):
    """团队统计模型"""
    团队总数: int = Field(0, description="团队总数")
    活跃团队数: int = Field(0, description="活跃团队数")
    成员总数: int = Field(0, description="成员总数")
    平均团队规模: float = Field(0.0, description="平均团队规模")

class 用户团队关系统计模型(BaseModel):
    """用户团队关系统计模型"""
    总团队数: int = Field(0, description="用户参与的总团队数")
    创建的团队数: int = Field(0, description="用户创建的团队数")
    管理的团队数: int = Field(0, description="用户管理的团队数")
    参与的团队数: int = Field(0, description="用户参与的团队数")

class 公司统计模型(BaseModel):
    """公司统计模型"""
    公司总数: int = Field(0, description="公司总数")
    活跃公司数: int = Field(0, description="活跃公司数")
    团队总数: int = Field(0, description="团队总数")
    员工总数: int = Field(0, description="员工总数")

class 团队成员信息模型(BaseModel):
    """团队成员信息模型"""
    用户id: int = Field(..., description="用户id")
    用户名: Optional[str] = Field(None, description="用户名")
    手机号: Optional[str] = Field(None, description="手机号")
    邮箱: Optional[str] = Field(None, description="邮箱")
    昵称: Optional[str] = Field(None, description="昵称")
    头像: Optional[str] = Field(None, description="头像URL")
    职位: Optional[str] = Field(None, description="在团队中的职位")
    加入时间: Optional[datetime] = Field(None, description="加入团队时间")
    状态: Optional[str] = Field(None, description="成员状态")
    权限列表: Optional[List[str]] = Field([], description="用户在团队中的权限列表") 