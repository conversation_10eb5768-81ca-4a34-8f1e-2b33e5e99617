<template>
  <div class="company-approval-container p-4">
    <h1 class="text-2xl font-bold mb-4">公司审核</h1>

    <div class="mb-4">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="pending" tab="待审核"></a-tab-pane>
        <a-tab-pane key="reviewed" tab="已审核"></a-tab-pane>
      </a-tabs>
    </div>

    <a-spin :spinning="loading">
      <a-table
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        @change="handleTableChange"
        :rowKey="record => record.公司id"
      />
    </a-spin>

    <!-- 审核模态框 -->
    <a-modal
      v-model:open="reviewModalVisible"
      :title="reviewModalTitle"
      @ok="handleReviewSubmit"
      :confirmLoading="reviewSubmitting"
      okText="提交"
      cancelText="取消"
      width="700px"
    >
      <a-form layout="vertical">
        <!-- 公司基本信息 -->
        <div class="review-company-info">
          <h4>公司信息</h4>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="公司名称">
                <a-input :value="currentCompany?.公司名称" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="公司简称">
                <a-input :value="currentCompany?.公司简称 || '无'" disabled />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="联系电话">
                <a-input :value="currentCompany?.联系电话 || '无'" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="邮箱">
                <a-input :value="currentCompany?.邮箱 || '无'" disabled />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="公司地址">
            <a-input :value="currentCompany?.公司地址 || '无'" disabled />
          </a-form-item>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="法人代表">
                <a-input :value="currentCompany?.法人代表 || '无'" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="申请人ID">
                <a-input :value="currentCompany?.创建人id || '无'" disabled />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 审核操作 -->
        <div class="review-action-section">
          <h4>审核操作</h4>
          <a-form-item label="审核结果">
            <a-radio-group :value="reviewAction" disabled>
              <a-radio value="approved">
                <span style="color: #52c41a;">✓ 批准通过</span>
              </a-radio>
              <a-radio value="rejected">
                <span style="color: #ff4d4f;">✗ 拒绝申请</span>
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            label="审核备注"
            :required="reviewAction === 'rejected'"
          >
            <a-textarea
              v-model:value="reviewForm.remarks"
              :rows="4"
              :placeholder="reviewAction === 'rejected' ? '拒绝时必须填写审核备注' : '请输入审核备注（可选）'"
            />
          </a-form-item>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { computed, h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../store';
import { Button, Col, Form, Input, message, Modal, Radio, Row, Space, Spin, Table, Tabs, Tag, Textarea } from 'ant-design-vue';
import { useSuperAdminRequest } from '../composables/useApiRequest';
import superAdminService from '../services/superAdminService';
import { formatDate } from '../utils/dateUtils';

const router = useRouter();

// API请求Hook
const { 执行API请求, loading: apiLoading } = useSuperAdminRequest();
const { 执行API请求: 执行审核请求, loading: reviewApiLoading } = useSuperAdminRequest();

// 响应式数据
const activeTab = ref('pending');
const dataSource = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
});

// 审核相关
const reviewModalVisible = ref(false);
const currentCompany = ref(null);
const reviewAction = ref('');
const reviewSubmitting = ref(false);
const reviewForm = reactive({
  remarks: '',
});

// 表格列定义
const columns = computed(() => {
  const cols = [
    {
      title: '公司信息',
      key: 'companyInfo',
      width: 250,
      customRender: ({ record }) => {
        return h('div', { class: 'company-info-cell' }, [
          h('div', { class: 'company-name' }, record.公司名称),
          record.公司简称 ? h('div', { class: 'company-code' }, `简称：${record.公司简称}`) : null,
          record.联系电话 ? h('div', { class: 'company-phone' }, `电话：${record.联系电话}`) : null,
          record.邮箱 ? h('div', { class: 'company-email' }, `邮箱：${record.邮箱}`) : null
        ]);
      }
    },
    {
      title: '申请人ID',
      dataIndex: '创建人id',
      key: 'applicant',
      width: 100,
      align: 'center',
      customRender: ({ record }) => record['创建人id'] || '-',
    },
    {
      title: '提交时间',
      dataIndex: '创建时间',
      key: 'submissionDate',
      width: 150,
      customRender: ({ text }) => formatDate(text)
    },
    {
      title: '状态',
      dataIndex: '审核状态',
      key: 'status',
      width: 100,
      align: 'center',
      customRender: ({ text }) => {
        // 处理整数状态：0=未审核，1=审核通过，2=审核不通过
        let statusText, color;
        if (text === 0 || text === '0') {
          statusText = '未审核';
          color = 'orange';
        } else if (text === 1 || text === '1') {
          statusText = '审核通过';
          color = 'green';
        } else if (text === 2 || text === '2') {
          statusText = '审核不通过';
          color = 'red';
        } else {
          // 兼容旧的中文状态
          statusText = text;
          color = text === '已通过' ? 'green' : text === '已拒绝' ? 'red' : 'orange';
        }
        return h(Tag, { color }, () => statusText);
      }
    },
  ];

  // 已审核标签页添加额外列
  if (activeTab.value === 'reviewed') {
    cols.push(
      {
        title: '审核人',
        dataIndex: '审核人姓名',
        key: 'reviewer',
      },
      {
        title: '审核时间',
        dataIndex: '审核时间',
        key: 'reviewDate',
        customRender: ({ text }) => formatDate(text)
      },
      {
        title: '审核备注',
        dataIndex: '审核备注',
        key: 'remarks',
      }
    );
  }

  // 操作列
  cols.push({
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center',
    customRender: ({ record }) => {
      if (activeTab.value === 'pending') {
        return h(Space, { size: 'small' }, () => [
          h(Button, {
            type: 'primary',
            size: 'small',
            loading: reviewSubmitting.value && currentCompany.value?.公司id === record.公司id,
            onClick: () => openReviewModal(record, 'approved')
          }, () => '批准'),
          h(Button, {
            type: 'danger',
            size: 'small',
            loading: reviewSubmitting.value && currentCompany.value?.公司id === record.公司id,
            onClick: () => openReviewModal(record, 'rejected')
          }, () => '拒绝'),
          h(Button, {
            type: 'default',
            size: 'small',
            onClick: () => showCompanyDetails(record)
          }, () => '详情')
        ]);
      } else {
        return h(Space, { size: 'small' }, () => [
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => showReviewDetails(record)
          }, () => '审核详情'),
          h(Button, {
            type: 'default',
            size: 'small',
            onClick: () => showCompanyDetails(record)
          }, () => '公司详情')
        ]);
      }
    }
  });

  return cols;
});

// 计算属性
const reviewModalTitle = computed(() => {
  if (!currentCompany.value) return '';
  return `${reviewAction.value === 'approved' ? '批准' : '拒绝'}公司：${currentCompany.value.公司名称}`;
});

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;

    const params = {
      页码: pagination.current,
      每页数量: pagination.pageSize,
    };

    let response;
    if (activeTab.value === 'pending') {
      response = await 执行API请求(() => superAdminService.getPendingCompanies(params));
    } else {
      response = await 执行API请求(() => superAdminService.getReviewedCompanies(params));
    }

    if (response && response.status === 100) {
      const data = response.data;
      // 后端返回的数据格式：{ list: [], total: 0, page: 1, size: 10, pages: 1 }
      dataSource.value = data?.list || [];
      pagination.total = data?.total || 0;
    } else {
      dataSource.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取数据异常:', error);
    dataSource.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 事件处理
const handleTableChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchData();
};

const handleTabChange = (key) => {
  activeTab.value = key;
  pagination.current = 1;
  fetchData();
};

const openReviewModal = (company, action) => {
  currentCompany.value = company;
  reviewAction.value = action;
  reviewForm.remarks = '';
  reviewModalVisible.value = true;
};

const handleReviewSubmit = async () => {
  if (reviewAction.value === 'rejected' && !reviewForm.remarks.trim()) {
    message.error('拒绝公司申请时，必须填写审核备注。');
    return;
  }

  // 使用后端期望的参数格式
  const payload = {
    公司id: currentCompany.value.公司id,
    审核备注: reviewForm.remarks.trim()
  };

  const actionText = reviewAction.value === 'approved' ? '批准' : '拒绝';

  try {
    reviewSubmitting.value = true;

    let response;
    if (reviewAction.value === 'approved') {
      response = await 执行审核请求(() => superAdminService.approveCompany(payload));
    } else {
      response = await 执行审核请求(() => superAdminService.rejectCompany(payload));
    }

    if (response && response.status === 100) {
      reviewModalVisible.value = false;
      reviewForm.remarks = '';
      currentCompany.value = null;
      reviewAction.value = '';
      message.success(`公司${actionText}操作成功！`);
      // 刷新数据
      await fetchData();
    } else {
      const errorMsg = response?.message || `${actionText}操作失败`;
      message.error(errorMsg);
      console.error('审核操作失败:', response);
    }
  } catch (error) {
    console.error('审核操作异常:', error);
    message.error(`公司${actionText}操作失败，请稍后再试。`);
  } finally {
    reviewSubmitting.value = false;
  }
};

const showReviewDetails = (company) => {
  const getStatusText = (status) => {
    if (status === 0 || status === '0') return '未审核';
    if (status === 1 || status === '1') return '审核通过';
    if (status === 2 || status === '2') return '审核不通过';
    return status;
  };

  Modal.info({
    title: '审核详情 - ' + company.公司名称,
    content: h('div', { class: 'review-details' }, [
      h('div', { class: 'detail-row' }, [
        h('span', { class: 'detail-label' }, '公司名称：'),
        h('span', { class: 'detail-value' }, company.公司名称)
      ]),
      h('div', { class: 'detail-row' }, [
        h('span', { class: 'detail-label' }, '申请人ID：'),
        h('span', { class: 'detail-value' }, company.创建人id || 'N/A')
      ]),
      h('div', { class: 'detail-row' }, [
        h('span', { class: 'detail-label' }, '提交时间：'),
        h('span', { class: 'detail-value' }, formatDate(company.创建时间))
      ]),
      h('div', { class: 'detail-row' }, [
        h('span', { class: 'detail-label' }, '审核状态：'),
        h('span', { class: 'detail-value' }, getStatusText(company.审核状态))
      ]),
      h('div', { class: 'detail-row' }, [
        h('span', { class: 'detail-label' }, '审核人：'),
        h('span', { class: 'detail-value' }, company.审核人姓名 || 'N/A')
      ]),
      h('div', { class: 'detail-row' }, [
        h('span', { class: 'detail-label' }, '审核时间：'),
        h('span', { class: 'detail-value' }, formatDate(company.审核时间) || 'N/A')
      ]),
      h('div', { class: 'detail-row' }, [
        h('span', { class: 'detail-label' }, '审核备注：'),
        h('span', { class: 'detail-value' }, company.审核备注 || '无')
      ])
    ]),
    width: 600,
  });
};

const showCompanyDetails = (company) => {
  Modal.info({
    title: '公司详情 - ' + company.公司名称,
    content: h('div', { class: 'company-details' }, [
      h('div', { class: 'detail-section' }, [
        h('h4', {}, '基本信息'),
        h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '公司名称：'),
          h('span', { class: 'detail-value' }, company.公司名称)
        ]),
        company.公司简称 ? h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '公司简称：'),
          h('span', { class: 'detail-value' }, company.公司简称)
        ]) : null,
        company.公司代码 ? h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '公司代码：'),
          h('span', { class: 'detail-value' }, company.公司代码)
        ]) : null,
        company.公司地址 ? h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '公司地址：'),
          h('span', { class: 'detail-value' }, company.公司地址)
        ]) : null
      ]),
      h('div', { class: 'detail-section' }, [
        h('h4', {}, '联系信息'),
        company.联系电话 ? h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '联系电话：'),
          h('span', { class: 'detail-value' }, company.联系电话)
        ]) : null,
        company.邮箱 ? h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '邮箱：'),
          h('span', { class: 'detail-value' }, company.邮箱)
        ]) : null,
        company.法人代表 ? h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '法人代表：'),
          h('span', { class: 'detail-value' }, company.法人代表)
        ]) : null,
        company.营业执照号 ? h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '营业执照号：'),
          h('span', { class: 'detail-value' }, company.营业执照号)
        ]) : null
      ]),
      company.备注 ? h('div', { class: 'detail-section' }, [
        h('h4', {}, '备注信息'),
        h('div', { class: 'detail-row' }, [
          h('span', { class: 'detail-label' }, '备注：'),
          h('span', { class: 'detail-value' }, company.备注)
        ])
      ]) : null
    ]),
    width: 700,
  });
};

// 组件挂载
onMounted(() => {
  const userStore = useUserStore();
  
  if (!userStore.isAuthenticated) {
    message.warning('请先登录后再访问此页面');
    router.push('/login');
    return;
  }
  
  fetchData();
});
</script>

<style scoped>
.company-approval-container {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
}

/* 公司信息单元格样式 */
.company-info-cell {
  line-height: 1.5;
}

.company-name {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
}

.company-code,
.company-phone,
.company-email {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 2px;
}

/* 详情弹窗样式 */
.review-details,
.company-details {
  line-height: 1.6;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.detail-row {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.detail-label {
  min-width: 100px;
  font-weight: 500;
  color: #595959;
  flex-shrink: 0;
}

.detail-value {
  color: #262626;
  word-break: break-all;
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 标签页样式 */
:deep(.ant-tabs-tab) {
  font-weight: 500;
}

:deep(.ant-tabs-tab-active) {
  font-weight: 600;
}

/* 审核模态框样式 */
.review-company-info,
.review-action-section {
  margin-bottom: 24px;
}

.review-company-info h4,
.review-action-section h4 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.review-action-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}
</style>