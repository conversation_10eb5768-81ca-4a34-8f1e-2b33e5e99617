/**
 * 进度管理服务
 * 提供状态选项和配置功能
 * 注意：产品对接进度功能已移除，此文件仅提供基础配置支持
 */

class ProgressService {
  /**
   * 获取综合状态选项
   * @returns {Array} 状态选项列表
   */
  getOverallStatusOptions() {
    return [
      { label: '未开始', value: 0, color: '#d9d9d9' },
      { label: '进行中', value: 1, color: '#1890ff' },
      { label: '已完成', value: 2, color: '#52c41a' },
      { label: '已暂停', value: 3, color: '#faad14' },
      { label: '已取消', value: 4, color: '#f5222d' }
    ];
  }

  /**
   * 获取意向状态选项
   * @returns {Array} 意向状态选项列表
   */
  getIntentionStatusOptions() {
    return [
      { label: '未确认', value: 0, color: '#d9d9d9' },
      { label: '有意向', value: 1, color: '#52c41a' },
      { label: '无意向', value: 2, color: '#f5222d' },
      { label: '待跟进', value: 3, color: '#faad14' }
    ];
  }

  /**
   * 获取样品状态选项
   * @returns {Array} 样品状态选项列表
   */
  getSampleStatusOptions() {
    return [
      { label: '未寄送', value: 0, color: '#d9d9d9' },
      { label: '已寄送', value: 1, color: '#1890ff' },
      { label: '已收到', value: 2, color: '#52c41a' },
      { label: '已退回', value: 3, color: '#faad14' }
    ];
  }

  /**
   * 获取排期状态选项
   * @returns {Array} 排期状态选项列表
   */
  getScheduleStatusOptions() {
    return [
      { label: '未排期', value: 0, color: '#d9d9d9' },
      { label: '已排期', value: 1, color: '#1890ff' },
      { label: '已确认', value: 2, color: '#52c41a' },
      { label: '已取消', value: 3, color: '#f5222d' }
    ];
  }

  /**
   * 获取状态标签配置
   * @param {string} statusType - 状态类型
   * @param {number} statusValue - 状态值
   * @returns {{color: string, text: string}} 状态配置
   */
  getStatusTagConfig(statusType, statusValue) {
    const statusMaps = {
      '回复状态': this.getOverallStatusOptions(),
      '意向状态': this.getIntentionStatusOptions(),
      '样品状态': this.getSampleStatusOptions(),
      '排期状态': this.getScheduleStatusOptions(),
      '综合状态': this.getOverallStatusOptions()
    };

    const statusOptions = statusMaps[statusType] || this.getOverallStatusOptions();
    const statusOption = statusOptions.find(option => option.value === statusValue);

    if (statusOption) {
      return {
        color: statusOption.color,
        text: statusOption.label
      };
    }

    // 默认配置
    return {
      color: '#d9d9d9',
      text: '未知状态'
    };
  }

  /**
   * 获取进度数据（已移除功能的占位方法）
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 空数据
   */
  async getProgressData(params) {
    console.warn('产品对接进度功能已移除');
    return {
      status: 200,
      message: '产品对接进度功能已移除',
      data: []
    };
  }

  /**
   * 创建进度记录（已移除功能的占位方法）
   * @param {Object} data - 进度数据
   * @returns {Promise<Object>} 操作结果
   */
  async createProgress(data) {
    console.warn('产品对接进度功能已移除');
    return {
      status: 200,
      message: '产品对接进度功能已移除',
      data: null
    };
  }

  /**
   * 更新进度记录（已移除功能的占位方法）
   * @param {string} id - 进度ID
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 操作结果
   */
  async updateProgress(id, data) {
    console.warn('产品对接进度功能已移除');
    return {
      status: 200,
      message: '产品对接进度功能已移除',
      data: null
    };
  }

  /**
   * 删除进度记录（已移除功能的占位方法）
   * @param {string} id - 进度ID
   * @returns {Promise<Object>} 操作结果
   */
  async deleteProgress(id) {
    console.warn('产品对接进度功能已移除');
    return {
      status: 200,
      message: '产品对接进度功能已移除',
      data: null
    };
  }
}

// 创建服务实例
const progressService = new ProgressService();

export default progressService;
