"""
团队基础数据操作模块
负责处理团队基本信息的数据库操作
"""

from datetime import datetime
from typing import Optional, Dict, Any, List

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 数据库日志器
from 状态 import 状态

# 从工具模块导入记录团队操作日志函数
from 工具.团队工具 import 记录团队操作日志


async def 创建团队(
    团队名称: str,
    公司id: int,
    创建人id: int,
    团队代码: Optional[str] = None,
    团队描述: Optional[str] = None,
    团队负责人id: Optional[int] = None,
    备注: Optional[str] = None
) -> Dict[str, Any]:
    """创建团队"""
    try:
        # 检查用户创建团队数量限制
        可创建总数 = await 获取用户可创建团队总数(创建人id)
        已创建数 = await 获取用户已创建团队数(创建人id)

        if 已创建数 >= 可创建总数:
            数据库日志器.warning(f"用户 {创建人id} 创建团队数量已达上限: {已创建数}/{可创建总数}")
            return {
                "success": False,
                "status": 状态.团队管理.创建数量已达上限,
                "message": f"您已达到创建团队数量上限（{已创建数}/{可创建总数}），请通过开通会员或充值提升创建团队数量"
            }

        # 自动获取用户的最大团队人数上限（基于会员权限）
        最大成员数 = await 获取用户最大团队人数上限(创建人id)
        数据库日志器.info(f"用户 {创建人id} 创建团队《{团队名称}》，自动设置最大成员数: {最大成员数}（基于会员权限）")

        # 检查团队名称在公司内是否重复
        if await 检查团队名称重复(团队名称, 公司id):
            return {"success": False, "message": "该公司内团队名称已存在"}
        
        # 统一处理可选参数，将空字符串转换为None
        参数字典 = {
            "团队代码": None if 团队代码 == '' else 团队代码,
            "团队描述": None if 团队描述 == '' else 团队描述,
            "团队负责人id": None if 团队负责人id == 0 else 团队负责人id,
            "备注": None if 备注 == '' else 备注
        }

        # 构建插入SQL和参数
        SQL = """
        INSERT INTO 团队表
        (团队名称, 团队代码, 团队描述, 公司id, 团队负责人id, 最大成员数, 当前成员数, 创建人id, 备注)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        """
        
        参数 = (
            团队名称, 参数字典["团队代码"], 参数字典["团队描述"], 公司id, 
            参数字典["团队负责人id"], 最大成员数, 0, 创建人id, 参数字典["备注"]
        )

        团队id = await 异步连接池实例.执行插入(SQL, 参数)
        
        if 团队id:
            # 创建者自动加入团队
            from 数据.团队成员数据 import 加入团队
            await 加入团队(用户id=创建人id, 团队id=团队id, 职位="创建者")
            
            数据库日志器.info(f"创建团队成功，团队id: {团队id}，创建人: {创建人id}")
            
            # 记录团队操作日志
            await 记录团队操作日志(
                团队id=团队id,
                操作人ID=创建人id,
                操作类型="创建团队",
                操作内容=f"创建团队《{团队名称}》",
                备注=f"团队代码: {参数字典['团队代码']}, 最大成员数: {最大成员数}"
            )
            
            return {"success": True, "team_id": 团队id, "message": "团队创建成功"}
        else:
            错误日志器.error(f"团队数据插入失败: 团队名称='{团队名称}', 创建人id={创建人id}")
            return {"success": False, "message": "团队数据保存失败，请稍后重试"}
            
    except Exception as e:
        错误日志器.error(f"创建团队过程中发生异常: {e}", exc_info=True)
        return {"success": False, "message": "创建团队服务遇到内部错误，请稍后尝试"}


async def 检查团队名称重复(团队名称: str, 公司id: int) -> bool:
    """检查团队名称在公司内是否重复"""
    try:
        SQL = "SELECT 1 FROM 团队表 WHERE 团队名称 = $1 AND 公司id = $2 LIMIT 1"
        结果 = await 异步连接池实例.执行查询(SQL, (团队名称, 公司id))
        return len(结果) > 0
    except Exception as e:
        错误日志器.error(f"检查团队名称重复失败: {e}")
        return False


async def 获取团队基本信息(团队id: int) -> Optional[Dict[str, Any]]:
    """获取团队基本信息"""
    try:
        SQL = """
        SELECT 
            t.id as 团队id,
            t.团队名称, t.团队代码, t.团队描述, t.团队状态,
            t.最大成员数, t.当前成员数, t.创建时间, t.更新时间, t.备注,
            t.公司id, t.创建人id, t.团队负责人id,
            c.公司名称, c.公司简称,
                          COALESCE(leader.昵称, leader.手机号, '') as 团队负责人姓名,
            COALESCE(creator.昵称, creator.手机号, '') as 创建人姓名
        FROM 团队表 t
        LEFT JOIN 公司表 c ON t.公司id = c.id
        LEFT JOIN 用户表 leader ON t.团队负责人id = leader.id
        LEFT JOIN 用户表 creator ON t.创建人id = creator.id
        WHERE t.id = $1
        """
        
        结果 = await 异步连接池实例.执行查询(SQL, (团队id,))
        return 结果[0] if 结果 else None
        
    except Exception as e:
        错误日志器.error(f"获取团队基本信息失败: 团队id={团队id}, 错误={e}")
        return None


async def 解散团队(团队id: int, 操作用户id: int) -> Dict[str, Any]:
    """解散团队 - 直接删除团队记录，不可恢复"""
    try:
        # 先获取团队信息用于日志记录
        团队信息SQL = "SELECT 团队名称, 创建人id FROM 团队表 WHERE id = $1"
        团队信息结果 = await 异步连接池实例.执行查询(团队信息SQL, (团队id,))
        
        if not 团队信息结果:
            return {"success": False, "message": "团队不存在"}
            
        团队名称 = 团队信息结果[0]["团队名称"]
        创建人id = 团队信息结果[0]["创建人id"]
        
        # 记录最后一条操作日志（删除前记录）
        await 记录团队操作日志(
            团队id=团队id,
            操作人ID=操作用户id,
            操作类型="解散团队",
            操作内容=f"彻底解散团队《{团队名称}》，所有数据将被删除且无法恢复",
            备注=f"解散时间: {datetime.now().isoformat()}, 创建人id: {创建人id}"
        )
        
        # 直接删除团队记录，外键约束会自动级联删除相关数据
        # 包括：团队操作日志表、用户团队关联表、用户团队权限表
        删除SQL = "DELETE FROM 团队表 WHERE id = $1"
        影响行数 = await 异步连接池实例.执行数据库删除(删除SQL, (团队id,))
        
        if 影响行数 > 0:
            数据库日志器.info(f"团队已彻底删除: 团队id={团队id}, 团队名称=《{团队名称}》, 操作者={操作用户id}")
            return {"success": True, "message": f"团队《{团队名称}》已彻底解散，所有相关数据已删除且无法恢复"}
        else:
            return {"success": False, "message": "团队解散失败，团队可能不存在"}
            
    except Exception as e:
        错误日志器.error(f"解散团队失败: 团队id={团队id}, 错误={e}", exc_info=True)
        return {"success": False, "message": f"解散团队失败: {str(e)}"}


async def 获取团队成员信息(团队id: int, 用户id: int) -> Optional[Dict[str, Any]]:
    """获取指定团队中指定成员的详细信息"""
    try:
        SQL = """
        SELECT 
            ut.用户id, ut.团队id, ut.职位, ut.状态, ut.加入时间,
            COALESCE(u.昵称, u.手机号, '') as 昵称,
            u.手机号,
            u.邮箱
        FROM 用户团队关联表 ut
        LEFT JOIN 用户表 u ON ut.用户id = u.id
        WHERE ut.团队id = $1 AND ut.用户id = $2
        """
        
        结果 = await 异步连接池实例.执行查询(SQL, (团队id, 用户id))
        
        if not 结果:
            数据库日志器.warning(f"未找到指定成员: 团队id={团队id}, 用户id={用户id}")
            return None
        
        成员信息 = 结果[0]
        数据库日志器.info(f"获取团队成员信息成功: 团队id={团队id}, 用户id={用户id}")
        
        return {
            "用户id": 成员信息["用户id"],
            "团队id": 成员信息["团队id"],
            "昵称": 成员信息["昵称"],
            "手机号": 成员信息["手机号"],
            "邮箱": 成员信息["邮箱"],
            "职位": 成员信息["职位"],
            "状态": 成员信息["状态"],
            "加入时间": 成员信息["加入时间"]
        }
        
    except Exception as e:
        错误日志器.error(f"获取团队成员信息失败: 团队id={团队id}, 用户id={用户id}, 错误={e}")
        return None


def 格式化团队详情数据(
    团队信息: Dict[str, Any],
    用户权限: Optional[Dict[str, Any]] = None,
    成员统计: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """格式化团队详情数据，用于前端显示"""
    try:
        # 基础团队信息
        格式化数据 = {
            "团队id": 团队信息.get("团队id"),
            "团队名称": 团队信息.get("团队名称", ""),
            "团队代码": 团队信息.get("团队代码", ""),
            "团队描述": 团队信息.get("团队描述", ""),
            "团队状态": 团队信息.get("团队状态", "正常"),
            "最大成员数": 团队信息.get("最大成员数", 100),
            "当前成员数": 团队信息.get("当前成员数", 0),
            "创建时间": 团队信息.get("创建时间"),
            "更新时间": 团队信息.get("更新时间"),
            "备注": 团队信息.get("备注", ""),
            
            # 公司信息
            "公司id": 团队信息.get("公司id"),
            "公司名称": 团队信息.get("公司名称", ""),
            "公司简称": 团队信息.get("公司简称", ""),
            
            # 负责人和创建人信息
            "创建人id": 团队信息.get("创建人id"),
            "创建人姓名": 团队信息.get("创建人姓名", ""),
            "团队负责人id": 团队信息.get("团队负责人id"),
            "团队负责人姓名": 团队信息.get("团队负责人姓名", ""),
            
            # 格式化时间
            "创建时间格式化": "",
            "更新时间格式化": ""
        }
        
        # 安全地格式化时间
        try:
            if 团队信息.get("创建时间"):
                if isinstance(团队信息["创建时间"], str):
                    # 如果是字符串，尝试解析
                    from datetime import datetime
                    创建时间 = datetime.fromisoformat(团队信息["创建时间"].replace('Z', '+00:00'))
                    格式化数据["创建时间格式化"] = 创建时间.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    # 如果是datetime对象
                    格式化数据["创建时间格式化"] = 团队信息["创建时间"].strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            数据库日志器.warning(f"创建时间格式化失败: {e}")
            格式化数据["创建时间格式化"] = str(团队信息.get("创建时间", ""))
            
        try:
            if 团队信息.get("更新时间"):
                if isinstance(团队信息["更新时间"], str):
                    from datetime import datetime
                    更新时间 = datetime.fromisoformat(团队信息["更新时间"].replace('Z', '+00:00'))
                    格式化数据["更新时间格式化"] = 更新时间.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    格式化数据["更新时间格式化"] = 团队信息["更新时间"].strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            数据库日志器.warning(f"更新时间格式化失败: {e}")
            格式化数据["更新时间格式化"] = str(团队信息.get("更新时间", ""))
        
        # 用户权限信息（如果提供）
        if 用户权限:
            格式化数据.update({
                "用户角色": 用户权限.get("职位", "访客"),
                "用户状态": 用户权限.get("状态", "访客"),
                "是否创建者": 用户权限.get("是否团队创建者", False),
                "可否管理": 用户权限.get("能否管理团队", False),
                "在团队中": 用户权限.get("在团队中", False),
                "是否团队成员": 用户权限.get("是否团队成员", False),
                "权限列表": 用户权限.get("权限列表", [])
            })
        else:
            # 默认访客权限
            格式化数据.update({
                "用户角色": "访客",
                "用户状态": "访客",
                "是否创建者": False,
                "可否管理": False,
                "在团队中": False,
                "是否团队成员": False,
                "权限列表": ["查看团队信息"]
            })
        
        # 成员统计信息（如果提供）
        if 成员统计:
            格式化数据.update({
                "实际成员数": 成员统计.get("实际成员数", 0)
            })
            
        # 确保数据一致性：如果实际成员数和当前成员数不一致，以实际成员数为准
        if 成员统计 and "实际成员数" in 成员统计:
            if 成员统计["实际成员数"] != 格式化数据["当前成员数"]:
                数据库日志器.warning(
                    f"团队成员数不一致: 团队id={格式化数据['团队id']}, "
                    f"数据库记录={格式化数据['当前成员数']}, "
                    f"实际统计={成员统计['实际成员数']}"
                )
                格式化数据["当前成员数"] = 成员统计["实际成员数"]
        
        return 格式化数据
        
    except Exception as e:
        错误日志器.error(f"格式化团队详情数据失败: {e}", exc_info=True)
        # 返回最基本的数据结构，避免完全失败
        return {
            "团队id": 团队信息.get("团队id") if 团队信息 else None,
            "团队名称": 团队信息.get("团队名称", "") if 团队信息 else "",
            "错误": "数据格式化失败"
        }


async def 获取用户可创建团队总数(用户id: int) -> int:
    """获取用户可创建团队的总数量（用户表的可创建团队数 + 会员的可创建团队数）"""
    try:
        # 查询用户表的可创建团队数
        用户SQL = "SELECT COALESCE(可创建团队数, 0) as 用户可创建数 FROM 用户表 WHERE id = $1"
        用户结果 = await 异步连接池实例.执行查询(用户SQL, (用户id,))
        用户可创建数 = 用户结果[0]["用户可创建数"] if 用户结果 else 0

        # 查询用户当前有效会员的可创建团队数
        会员SQL = """
        SELECT COALESCE(m.可创建团队数, 0) as 会员可创建数
        FROM 用户_会员_关联表 um
        JOIN 会员表 m ON um.会员id = m.id
        WHERE um.用户id = $1
        AND TO_TIMESTAMP(um.到期时间, 'YYYY-MM-DD HH24:MI:SS') > NOW()
        ORDER BY m.可创建团队数 DESC
        LIMIT 1
        """
        会员结果 = await 异步连接池实例.执行查询(会员SQL, (用户id,))
        会员可创建数 = 会员结果[0]["会员可创建数"] if 会员结果 else 0

        总可创建数 = 用户可创建数 + 会员可创建数
        数据库日志器.info(f"用户 {用户id} 可创建团队总数: {总可创建数} (用户: {用户可创建数}, 会员: {会员可创建数})")

        return 总可创建数

    except Exception as e:
        错误日志器.error(f"获取用户可创建团队总数失败: 用户id={用户id}, 错误={e}")
        return 0


async def 转移团队所有权(团队id: int, 原所有者id: int, 新所有者id: int) -> Dict[str, Any]:
    """转移团队所有权"""
    try:
        from datetime import datetime

        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.transaction():
                # 1. 更新团队表的创建人id
                更新团队SQL = """
                UPDATE 团队表
                SET 创建人id = $1, 更新时间 = $2
                WHERE id = $3
                """
                await 连接.execute(
                    更新团队SQL, 新所有者id, datetime.now(), 团队id
                )

                # 2. 更新新所有者的职位为"创建者"
                更新新所有者SQL = """
                UPDATE 用户团队关联表
                SET 职位 = $1, 更新时间 = $2
                WHERE 用户id = $3 AND 团队id = $4
                """
                await 连接.execute(
                    更新新所有者SQL, "创建者", datetime.now(), 新所有者id, 团队id
                )

                # 3. 更新原创建者的职位为"成员"
                更新原创建者SQL = """
                UPDATE 用户团队关联表
                SET 职位 = $1, 更新时间 = $2
                WHERE 用户id = $3 AND 团队id = $4
                """
                await 连接.execute(
                    更新原创建者SQL, "成员", datetime.now(), 原所有者id, 团队id
                )

                # 4. 记录操作日志
                from 工具.团队工具 import 记录团队操作日志
                await 记录团队操作日志(
                    团队id=团队id,
                    操作人ID=原所有者id,
                    操作类型="转移所有权",
                    操作内容=f"将团队所有权转移给用户id:{新所有者id}",
                    备注=f"转移时间: {datetime.now().isoformat()}",
                )

        数据库日志器.info(f"团队所有权转移成功: 团队id={团队id}, 原所有者={原所有者id}, 新所有者={新所有者id}")
        return {"success": True, "message": "团队所有权转移成功"}

    except Exception as e:
        错误日志器.error(f"转移团队所有权失败: 团队id={团队id}, 错误={e}", exc_info=True)
        return {"success": False, "message": f"转移所有权失败: {str(e)}"}


async def 获取用户最近活动(用户id: int, 天数: int, 限制数量: int) -> List[Dict[str, Any]]:
    """获取用户在所有团队中的最近活动"""
    try:
        from datetime import datetime, timedelta

        # 计算查询的开始时间
        开始时间 = datetime.now() - timedelta(days=天数)

        # 查询用户在所有团队中的最近活动
        活动查询SQL = """
        SELECT
            tl.id as ID,
            tl.操作类型,
            tl.操作内容 as 活动描述,
            tl.操作时间 as 活动时间,
            u.昵称 as 操作人,
            t.团队名称,
            tl.操作对象ID as 目标用户id,
            tu.昵称 as 目标用户
        FROM 团队操作日志表 tl
        LEFT JOIN 用户表 u ON tl.操作人ID = u.id
        LEFT JOIN 团队表 t ON tl.团队id = t.id
        LEFT JOIN 用户表 tu ON tl.操作对象ID = tu.id
        WHERE tl.操作人ID = $1
        AND tl.操作时间 >= $2
        AND t.团队状态 != '解散'
        ORDER BY tl.操作时间 DESC
        LIMIT $3
        """

        活动记录 = await 异步连接池实例.执行查询(
            活动查询SQL, (用户id, 开始时间, 限制数量)
        )

        # 格式化活动记录
        活动列表 = []
        for 记录 in 活动记录:
            活动项 = {
                "ID": 记录.get("ID"),
                "操作类型": 记录.get("操作类型", "未知操作"),
                "活动描述": 记录.get("活动描述", "进行了操作"),
                "活动时间": 记录.get("活动时间"),
                "操作人": 记录.get("操作人", "未知用户"),
                "团队名称": 记录.get("团队名称"),
                "目标用户": 记录.get("目标用户"),
            }
            活动列表.append(活动项)

        数据库日志器.info(f"获取用户 {用户id} 最近活动成功，共 {len(活动列表)} 条记录")
        return 活动列表

    except Exception as e:
        错误日志器.error(f"获取用户最近活动失败: 用户id={用户id}, 错误={e}", exc_info=True)
        return []


async def 获取用户已创建团队数(用户id: int) -> int:
    """获取用户已创建的团队数量"""
    try:
        SQL = """
        SELECT COUNT(*) as 已创建数
        FROM 团队表
        WHERE 创建人id = $1 AND 团队状态 != '解散'
        """
        结果 = await 异步连接池实例.执行查询(SQL, (用户id,))
        已创建数 = 结果[0]["已创建数"] if 结果 else 0

        数据库日志器.info(f"用户 {用户id} 已创建团队数: {已创建数}")
        return 已创建数

    except Exception as e:
        错误日志器.error(f"获取用户已创建团队数失败: 用户id={用户id}, 错误={e}")
        return 0


async def 获取用户最大团队人数上限(用户id: int) -> int:
    """获取用户的最大团队人数上限（基于用户开通的有效会员中创建团队默认人数上限最大值）"""
    try:
        # 查询用户当前所有有效会员的创建团队默认人数上限，按上限数值降序排列
        会员SQL = """
        SELECT COALESCE(m.创建团队默认人数上限, 0) as 人数上限
        FROM 用户_会员_关联表 um
        JOIN 会员表 m ON um.会员id = m.id
        WHERE um.用户id = $1
        AND TO_TIMESTAMP(um.到期时间, 'YYYY-MM-DD HH24:MI:SS') > NOW()
        AND m.创建团队默认人数上限 > 0
        ORDER BY m.创建团队默认人数上限 DESC
        LIMIT 1
        """
        会员结果 = await 异步连接池实例.执行查询(会员SQL, (用户id,))
        
        if 会员结果:
            最大人数上限 = 会员结果[0]["人数上限"]
            数据库日志器.info(f"用户 {用户id} 最大团队人数上限: {最大人数上限}（基于有效会员权限）")
            return 最大人数上限
        else:
            # 如果没有有效会员或会员未设置人数上限，使用默认值
            默认上限 = 100  # 系统默认团队人数上限
            数据库日志器.info(f"用户 {用户id} 无有效会员人数上限配置，使用默认值: {默认上限}")
            return 默认上限
            
    except Exception as e:
        错误日志器.error(f"获取用户最大团队人数上限异常: {e}", exc_info=True)
        return 100  # 异常时返回默认值
