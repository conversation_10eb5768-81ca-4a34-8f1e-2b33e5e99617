"""
LangChain结构化输出处理器 - Context7优化版本
专注于使用LangChain官方的with_structured_output方法
支持Pydantic模型验证、代码执行和示例数据生成
"""

import importlib.util
import inspect
import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Type, Union

try:
    from typing import get_args, get_origin
except ImportError:
    # Python < 3.8兼容性
    from typing_extensions import get_args, get_origin

from pydantic import BaseModel, Field, ValidationError, create_model

# 配置日志
from 日志 import 应用日志器 as 结构化输出日志器


def _安全获取类型信息(type_hint):
    """安全地获取类型信息，避免属性访问错误"""
    try:
        origin = get_origin(type_hint)
        args = get_args(type_hint)
        return origin, args
    except (AttributeError, TypeError):
        return None, ()


def _是联合类型(type_hint):
    """安全地检查是否为Union类型"""
    origin, _ = _安全获取类型信息(type_hint)
    return origin is Union


def _是列表类型(type_hint):
    """安全地检查是否为List类型"""
    origin, _ = _安全获取类型信息(type_hint)
    return origin is list


def _是字典类型(type_hint):
    """安全地检查是否为Dict类型"""
    origin, _ = _安全获取类型信息(type_hint)
    return origin is dict


class LangChain结构化输出处理器:
    """
    Context7优化的结构化输出处理器
    专注于LangChain官方的with_structured_output方法
    """

    def __init__(self):
        """初始化结构化输出处理器"""
        self.已初始化 = False
        self.模型缓存 = {}
        self.验证历史 = []

    async def 初始化(self):
        """初始化处理器"""
        if not self.已初始化:
            结构化输出日志器.info("🚀 初始化LangChain结构化输出处理器")
            self._验证核心功能()
            self.已初始化 = True
            结构化输出日志器.info("✅ 结构化输出处理器初始化完成")

    def _验证核心功能(self):
        """验证核心功能"""
        try:
            # 验证Pydantic可用性
            结构化输出日志器.info("✅ Pydantic导入成功")

            # 验证LangChain可用性
            try:
                if importlib.util.find_spec(
                    "langchain_core.output_parsers"
                ) and importlib.util.find_spec("langchain_core.prompts"):
                    结构化输出日志器.info("✅ LangChain Core组件可用")
                else:
                    raise ImportError("LangChain Core组件不完整")
            except ImportError as e:
                结构化输出日志器.warning(f"⚠️ LangChain Core部分组件不可用: {e}")

        except Exception as e:
            结构化输出日志器.error(f"❌ 核心功能验证失败: {e}")
            raise

    async def 从Pydantic代码创建模型(self, pydantic_代码: str) -> Dict[str, Any]:
        """
        从Pydantic代码创建模型类 - Context7优化版本
        支持Pydantic v2最新语法和完整的错误处理
        """
        try:
            结构化输出日志器.info("🔧 开始从Pydantic代码创建模型 (Context7优化版本)")

            # 参数验证
            if not self._验证Pydantic代码格式(pydantic_代码):
                return {
                    "success": False,
                    "error": "Pydantic代码格式验证失败",
                    "suggestion": "请检查代码格式和语法",
                }

            # 创建安全的执行环境
            执行环境 = self._创建安全执行环境()

            # 执行Pydantic代码
            try:
                exec(pydantic_代码, 执行环境)
                结构化输出日志器.debug("✅ Pydantic代码执行成功")
            except SyntaxError as e:
                结构化输出日志器.error(f"❌ Python语法错误: {e}")
                return {
                    "success": False,
                    "error": f"Python语法错误: {str(e)}",
                    "suggestion": f"请检查第{e.lineno}行的语法",
                }
            except ImportError as e:
                结构化输出日志器.error(f"❌ 导入错误: {e}")
                return {
                    "success": False,
                    "error": f"导入错误: {str(e)}",
                    "suggestion": "请检查Pydantic导入语句",
                }
            except Exception as e:
                结构化输出日志器.error(f"❌ Pydantic代码执行失败: {e}")
                return {
                    "success": False,
                    "error": f"代码执行失败: {str(e)}",
                    "suggestion": "请检查代码逻辑和Pydantic语法",
                }

            # 查找定义的模型类
            模型类 = self._提取模型类(执行环境)
            if not 模型类:
                return {
                    "success": False,
                    "error": "未找到有效的Pydantic模型类",
                    "suggestion": "请确保代码中定义了继承自BaseModel的类",
                }

            # 验证模型功能 - Context7最佳实践
            验证结果 = await self._验证模型功能(模型类)
            if not 验证结果["valid"]:
                return {
                    "success": False,
                    "error": 验证结果["error"],
                    "suggestion": 验证结果.get("suggestion", ""),
                }

            # 生成示例数据
            示例数据 = self._生成模型示例数据(模型类)

            # 缓存模型
            模型缓存键 = f"model_{uuid.uuid4().hex[:8]}"
            self.模型缓存[模型缓存键] = {
                "模型类": 模型类,
                "创建时间": datetime.now(),
                "代码": pydantic_代码,
            }

            结构化输出日志器.info(f"✅ 模型创建成功: {模型类.__name__}")

            return {
                "success": True,
                "模型类": 模型类,
                "模型名称": 模型类.__name__,
                "缓存键": 模型缓存键,
                "示例数据": 示例数据,
                "字段信息": self._获取字段信息(模型类),
            }

        except Exception as e:
            结构化输出日志器.error(f"❌ 创建模型失败: {e}")
            return {
                "success": False,
                "error": f"创建模型失败: {str(e)}",
                "suggestion": "请检查代码格式和Pydantic语法",
            }

    def _创建安全执行环境(self) -> Dict[str, Any]:
        """创建安全的代码执行环境"""
        return {
            # Pydantic相关
            "BaseModel": BaseModel,
            "Field": Field,
            "ValidationError": ValidationError,
            "create_model": create_model,
            # Python标准类型
            "str": str,
            "int": int,
            "float": float,
            "bool": bool,
            "list": list,
            "dict": dict,
            "List": List,
            "Dict": Dict,
            "Optional": Optional,
            "Union": Union,
            "Any": Any,
            "Type": Type,
            # 时间相关
            "datetime": datetime,
            # 其他常用模块
            "json": json,
            "uuid": uuid,
            # Context7推荐的导入
            "__builtins__": {
                "__import__": __import__,  # 添加 __import__ 函数支持导入语句
                "__build_class__": __build_class__,  # 添加 __build_class__ 函数支持类定义
                "__name__": "__main__",  # 添加模块名称
                "len": len,
                "str": str,
                "int": int,
                "float": float,
                "bool": bool,
                "list": list,
                "dict": dict,
                "range": range,
                "enumerate": enumerate,
                "zip": zip,
                "max": max,
                "min": min,
                "sum": sum,
                "abs": abs,
                "round": round,
            },
        }

    def _提取模型类(self, 执行环境: Dict[str, Any]) -> Optional[Type[BaseModel]]:
        """从执行环境中提取模型类"""
        模型类列表 = []

        for 名称, 对象 in 执行环境.items():
            if (
                inspect.isclass(对象)
                and issubclass(对象, BaseModel)
                and 对象 != BaseModel
                and not 名称.startswith("_")
            ):
                模型类列表.append(对象)

        if not 模型类列表:
            return None

        # 如果有多个模型类，选择最后定义的（通常是主要模型）
        主模型 = 模型类列表[-1]
        结构化输出日志器.info(f"📋 选择模型类: {主模型.__name__}")

        return 主模型

    async def _验证模型功能(self, 模型类: Type[BaseModel]) -> Dict[str, Any]:
        """验证模型类的功能"""
        try:
            # 测试模型实例化
            try:
                # 生成测试数据进行验证
                测试数据 = self._生成模型示例数据(模型类)
                测试实例 = 模型类(**测试数据)
                结构化输出日志器.debug(f"✅ 模型实例化测试通过: {测试实例}")
            except Exception as e:
                return {
                    "valid": False,
                    "error": f"模型实例化失败: {str(e)}",
                    "suggestion": "请检查字段定义和类型约束",
                }

            # 测试JSON序列化
            try:
                json_输出 = 测试实例.model_dump()
                # 验证JSON序列化是否成功
                json.dumps(json_输出, ensure_ascii=False, indent=2)
                结构化输出日志器.debug("✅ JSON序列化测试通过")
            except Exception as e:
                return {
                    "valid": False,
                    "error": f"JSON序列化失败: {str(e)}",
                    "suggestion": "请检查字段类型是否支持JSON序列化",
                }

            return {"valid": True, "测试实例": 测试实例, "json_输出": json_输出}

        except Exception as e:
            return {
                "valid": False,
                "error": f"模型验证失败: {str(e)}",
                "suggestion": "请检查模型定义的完整性",
            }

    def _生成模型示例数据(self, 模型类: Type[BaseModel]) -> Dict[str, Any]:
        """
        基于Context7最佳实践生成模型示例数据
        智能分析字段类型和名称，生成合适的示例值
        """
        示例数据 = {}

        try:
            # 获取模型的字段信息
            字段定义 = 模型类.model_fields

            for 字段名, 字段信息 in 字段定义.items():
                示例值 = self._生成字段示例值(字段名, 字段信息)
                示例数据[字段名] = 示例值

        except Exception as e:
            结构化输出日志器.warning(f"⚠️ 生成示例数据失败: {e}")
            # 返回基础示例数据
            示例数据 = {"示例字段": "示例值"}

        return 示例数据

    def _生成字段示例值(self, 字段名: str, 字段信息) -> Any:
        """根据字段名和类型生成合适的示例值"""
        字段名_lower = 字段名.lower()

        # 首先检查字段的实际类型，优先于字段名
        field_type = getattr(字段信息, "annotation", str)

        # 处理Optional类型
        if _是联合类型(field_type):
            _, args = _安全获取类型信息(field_type)
            non_none_types = [t for t in args if t is not type(None)]
            if non_none_types:
                field_type = non_none_types[0]

        # 如果是数组类型，优先返回数组
        if _是列表类型(field_type):
            if any(keyword in 字段名_lower for keyword in ["content", "内容", "回复"]):
                return [{"内容": "示例回复内容", "类型": "文本"}]
            elif any(keyword in 字段名_lower for keyword in ["list", "列表"]):
                return ["项目1", "项目2", "项目3"]
            else:
                return ["示例项目1", "示例项目2"]

        # 如果是字典类型，返回字典
        if _是字典类型(field_type):
            return {"键1": "值1", "键2": "值2"}

        # 基于字段名生成智能示例值（仅用于基本类型）
        if any(keyword in 字段名_lower for keyword in ["name", "名称", "姓名"]):
            return "张三"
        elif any(keyword in 字段名_lower for keyword in ["email", "邮箱"]):
            return "<EMAIL>"
        elif any(keyword in 字段名_lower for keyword in ["phone", "电话", "手机"]):
            return "13800138000"
        elif any(keyword in 字段名_lower for keyword in ["age", "年龄"]):
            return 25
        elif any(keyword in 字段名_lower for keyword in ["price", "价格", "金额"]):
            return 99.99
        elif any(keyword in 字段名_lower for keyword in ["count", "数量", "个数"]):
            return 10
        elif any(keyword in 字段名_lower for keyword in ["date", "日期", "时间"]):
            return datetime.now().isoformat()
        elif any(keyword in 字段名_lower for keyword in ["url", "链接"]):
            return "https://example.com"
        elif any(keyword in 字段名_lower for keyword in ["desc", "描述", "说明"]):
            return "这是一个示例描述"
        elif any(keyword in 字段名_lower for keyword in ["title", "标题"]):
            return "示例标题"
        elif any(keyword in 字段名_lower for keyword in ["content", "内容"]):
            return "这是示例内容"
        elif any(keyword in 字段名_lower for keyword in ["confidence", "置信度"]):
            if field_type is str:
                return "0.85"
            else:
                return 0.85
        elif any(keyword in 字段名_lower for keyword in ["priority", "优先级"]):
            return "高"
        elif any(keyword in 字段名_lower for keyword in ["status", "状态"]):
            return "正常"
        elif any(keyword in 字段名_lower for keyword in ["id", "ID"]):
            return "12345"
        elif any(keyword in 字段名_lower for keyword in ["bool", "布尔", "是否"]):
            return True
        else:
            # 默认基于类型生成
            return self._生成默认类型值(字段信息)

    def _生成默认类型值(self, 字段信息) -> Any:
        """基于字段类型生成默认值"""
        try:
            # 获取字段的注解类型
            field_type = 字段信息.annotation

            # 处理Optional类型
            if _是联合类型(field_type):
                # 获取非None的类型
                _, args = _安全获取类型信息(field_type)
                non_none_types = [t for t in args if t is not type(None)]
                if non_none_types:
                    field_type = non_none_types[0]

            # 基于类型生成示例值
            if field_type is str:
                return "示例文本"
            elif field_type is int:
                return 42
            elif field_type is float:
                return 3.14
            elif field_type is bool:
                return True
            elif field_type is datetime:
                return datetime.now()
            elif _是列表类型(field_type):
                return ["项目1", "项目2"]
            elif _是字典类型(field_type):
                return {"键": "值"}

            return "示例值"

        except Exception:
            return "示例值"

    def _获取字段信息(self, 模型类: Type[BaseModel]) -> List[Dict[str, Any]]:
        """获取模型的字段信息"""
        字段信息列表 = []

        try:
            字段定义 = 模型类.model_fields

            for 字段名, 字段信息 in 字段定义.items():
                字段信息列表.append(
                    {
                        "字段名": 字段名,
                        "字段类型": str(字段信息.annotation),
                        "是否必需": 字段信息.is_required(),
                        "默认值": str(字段信息.default)
                        if 字段信息.default is not None
                        else "无",
                        "描述": 字段信息.description or "无描述",
                    }
                )

        except Exception as e:
            结构化输出日志器.warning(f"⚠️ 获取字段信息失败: {e}")

        return 字段信息列表

    async def 验证Pydantic模型定义(
        self, 模型定义: str, 模式: str = "with_structured_output"
    ) -> Dict[str, Any]:
        """
        验证Pydantic模型定义 - Context7优化版本
        """
        try:
            结构化输出日志器.info(f"🔍 开始验证Pydantic模型定义 (模式: {模式})")

            # 检查代码基本格式
            if not 模型定义.strip():
                return {
                    "valid": False,
                    "error": "模型定义不能为空",
                    "suggestion": "请输入有效的Pydantic模型代码",
                }

            # 创建模型
            创建结果 = await self.从Pydantic代码创建模型(模型定义)

            if not 创建结果["success"]:
                return {
                    "valid": False,
                    "error": 创建结果["error"],
                    "suggestion": 创建结果.get("suggestion", ""),
                }

            模型类 = 创建结果["模型类"]
            示例数据 = 创建结果["示例数据"]
            字段信息 = 创建结果["字段信息"]

            # 生成JSON输出示例
            try:
                示例实例 = 模型类(**示例数据)
                json_输出 = json.dumps(
                    示例实例.model_dump(), ensure_ascii=False, indent=2
                )
            except Exception as e:
                json_输出 = f"JSON生成失败: {str(e)}"

            # 记录验证历史
            验证记录 = {
                "时间": datetime.now(),
                "模型名称": 模型类.__name__,
                "验证成功": True,
                "字段数量": len(字段信息),
            }
            self.验证历史.append(验证记录)

            结构化输出日志器.info(f"✅ Pydantic模型验证成功: {模型类.__name__}")

            return {
                "valid": True,
                "模型名称": 模型类.__name__,
                "字段数量": len(字段信息),
                "字段信息": 字段信息,
                "JSON输出": json_输出,
                "示例数据": 示例数据,
                "建议": f"模型 {模型类.__name__} 定义完整，支持 {模式} 模式",
            }

        except Exception as e:
            结构化输出日志器.error(f"❌ Pydantic模型验证失败: {e}")
            return {
                "valid": False,
                "error": f"验证失败: {str(e)}",
                "suggestion": "请检查Pydantic语法和字段定义",
            }

    async def 应用结构化输出到智能体(
        self, 智能体实例, pydantic_模型: Type[BaseModel]
    ) -> bool:
        """
        将结构化输出应用到智能体实例
        使用LangChain官方的with_structured_output方法
        """
        try:
            结构化输出日志器.info(f"🔧 应用结构化输出到智能体: {智能体实例.实例ID}")

            # 更新智能体配置
            智能体实例.配置.输出格式 = "structured"
            智能体实例.配置.自定义配置["pydantic_模型类"] = pydantic_模型
            智能体实例.配置.自定义配置["结构化输出模式"] = "with_structured_output"

            结构化输出日志器.info("✅ 结构化输出配置已应用")
            return True

        except Exception as e:
            结构化输出日志器.error(f"❌ 应用结构化输出失败: {e}")
            return False

    def _验证Pydantic代码格式(self, pydantic_代码: str) -> bool:
        """
        验证Pydantic代码格式 - Context7最佳实践
        """
        try:
            if not pydantic_代码 or not pydantic_代码.strip():
                结构化输出日志器.error("❌ Pydantic代码不能为空")
                return False

            # 检查基本的Pydantic导入
            if (
                "from pydantic import" not in pydantic_代码
                and "import pydantic" not in pydantic_代码
            ):
                结构化输出日志器.warning("⚠️ 代码中可能缺少Pydantic导入")

            # 检查BaseModel继承
            if "BaseModel" not in pydantic_代码:
                结构化输出日志器.warning("⚠️ 代码中可能缺少BaseModel继承")

            # 检查基本的Python语法
            try:
                compile(pydantic_代码, "<string>", "exec")
                结构化输出日志器.info("✅ Pydantic代码语法检查通过")
                return True
            except SyntaxError as e:
                结构化输出日志器.error(f"❌ Python语法错误: {e}")
                return False

        except Exception as e:
            结构化输出日志器.error(f"❌ Pydantic代码格式验证异常: {e}")
            return False

    async def 从JSON_Schema创建Pydantic模型(
        self, json_schema: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        从JSON Schema创建Pydantic模型 - Context7最佳实践
        与智能体服务层的方法保持一致
        """
        try:
            from pydantic import ConfigDict, Field, create_model

            结构化输出日志器.info("🔧 从JSON Schema创建Pydantic模型")

            # 验证JSON Schema格式
            if not self._验证JSON_Schema格式(json_schema):
                return {
                    "success": False,
                    "error": "JSON Schema格式验证失败",
                    "suggestion": "请检查JSON Schema的格式和必需字段",
                }

            properties = json_schema.get("properties", {})
            required_fields = json_schema.get("required", [])

            # 构建字段定义
            field_definitions = {}
            for field_name, field_def in properties.items():
                field_type = self._映射JSON类型到Python类型(
                    field_def.get("type", "string")
                )
                field_description = field_def.get("description", f"{field_name}字段")
                is_required = field_name in required_fields

                if is_required:
                    field_definitions[field_name] = (
                        field_type,
                        Field(..., description=field_description),
                    )
                else:
                    default_value = field_def.get("default", None)
                    field_definitions[field_name] = (
                        field_type,
                        Field(default=default_value, description=field_description),
                    )

            # 创建模型配置
            model_name = json_schema.get("title", "DynamicStructuredOutput")
            model_config = ConfigDict(
                str_strip_whitespace=True,
                validate_assignment=True,
                extra="forbid",
                use_enum_values=True,
                arbitrary_types_allowed=False,
            )

            # 动态创建模型
            dynamic_model = create_model(
                model_name, **field_definitions, __config__=model_config
            )

            dynamic_model.model_config = model_config
            dynamic_model.__doc__ = json_schema.get(
                "description", "动态生成的结构化输出模型"
            )

            # 生成示例数据
            示例数据 = self._生成模型示例数据(dynamic_model)

            结构化输出日志器.info(f"✅ 从JSON Schema成功创建模型: {model_name}")

            return {
                "success": True,
                "模型类": dynamic_model,
                "模型名称": model_name,
                "示例数据": 示例数据,
                "字段信息": self._获取字段信息(dynamic_model),
            }

        except Exception as e:
            结构化输出日志器.error(f"❌ 从JSON Schema创建模型失败: {e}")
            return {
                "success": False,
                "error": f"创建模型失败: {str(e)}",
                "suggestion": "请检查JSON Schema格式和字段定义",
            }

    def _验证JSON_Schema格式(self, json_schema: Dict[str, Any]) -> bool:
        """
        验证JSON Schema格式 - 使用统一验证器
        """
        from 服务.LangChain_JSON_Schema验证器 import json_schema_validator

        return json_schema_validator.validate_json_schema_format(
            json_schema, 结构化输出日志器
        )

    def _映射JSON类型到Python类型(self, json_type: str):
        """映射JSON类型到Python类型"""
        type_mapping = {
            "string": str,
            "integer": int,
            "number": float,
            "boolean": bool,
            "array": List[str],
            "object": Dict[str, Any],
        }
        return type_mapping.get(json_type, str)


# 创建全局实例
LangChain结构化输出处理器实例 = LangChain结构化输出处理器()
