"""
微信管理异步服务
提供微信账号管理、好友管理、产品对接进度、消息管理、统计数据、群管理等功能
"""

from datetime import datetime, timedelta
import pytz
from typing import Any, Dict, Optional, List, Union

import 状态

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 数据访问层导入
from 数据.异步微信数据访问层 import (
    异步数据_更新微信好友下次沟通时间,
    异步数据_查询微信好友消息详情,
    异步数据_获取微信好友下次沟通时间列表,
    异步数据_验证用户微信好友权限,
    异步数据_通过识别id更新微信好友信息,
    异步数据_查询用户微信关联信息,
    异步数据_查询微信号信息,
)
from 日志 import 系统日志器, 错误日志器

# 导入统一响应模型
from 数据模型.响应模型 import 统一响应模型

# 导入其他数据访问层
from 数据.用户微信好友请求状态数据访问层 import 用户微信好友请求状态数据访问层

# ================== 通用验证函数 ==================


def _验证用户id(用户id: int) -> Optional[Dict[str, Any]]:
    """验证用户id参数"""
    if not isinstance(用户id, int) or 用户id <= 0:
        return {"status": 状态.通用.参数错误, "message": "无效的用户id"}
    return None


def _验证微信号id(微信号id: int, 字段名: str = "微信号id") -> Optional[Dict[str, Any]]:
    """验证微信号id参数"""
    if not isinstance(微信号id, int) or 微信号id <= 0:
        return {"status": 状态.通用.参数错误, "message": f"无效的{字段名}"}
    return None


# ================== 微信账号管理服务 ==================


async def 异步获取微信账号列表服务(
    用户id: int,
    页码: int = 1,
    每页条数: int = 20,
    查询状态: Optional[str] = None,
    关键词: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取用户的微信账号列表

    Args:
        用户id: 用户标识，通过用户微信关联表筛选该用户的微信号
        页码: 页码，默认为1
        每页条数: 每页显示条数，默认为20
        查询状态: 关联状态筛选条件（1=正常，0=禁用）
        关键词: 搜索关键词，支持微信号搜索

    Returns:
        包含账号列表和分页信息的字典

    Note:
        通过用户微信关联表和微信信息表联查，获取用户拥有的微信号列表
    """
    try:
        # 构建查询参数 - 基于用户微信关联表和微信信息表联查
        查询参数: List[Union[int, str]] = [用户id]
        查询条件_列表 = ["uwx.用户id = $1"]
        参数索引 = 2

        # 添加状态筛选
        if 查询状态 is not None:
            查询条件_列表.append(f"uwx.状态 = ${参数索引}")
            查询参数.append(int(查询状态))
            参数索引 += 1

        # 添加关键词搜索
        if 关键词:
            查询条件_列表.append(f"wx.微信号 LIKE ${参数索引}")
            查询参数.append(f"%{关键词}%")
            参数索引 += 1

        查询条件 = "WHERE " + " AND ".join(查询条件_列表)

        # 获取总数
        总数查询 = f"""
        SELECT COUNT(*) as total 
        FROM 用户微信关联表 uwx 
        INNER JOIN 微信信息表 wx ON uwx.微信id = wx.id 
        {查询条件}
        """

        # 获取分页数据
        偏移量 = (页码 - 1) * 每页条数
        数据查询 = f"""
        SELECT wx.id, wx.微信号, wx.修改时间,
               uwx.绑定时间, uwx.状态, uwx.备注,
               uwx.更新时间 as 关联更新时间
        FROM 用户微信关联表 uwx
        INNER JOIN 微信信息表 wx ON uwx.微信id = wx.id
        {查询条件}
        ORDER BY uwx.绑定时间 DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        查询参数.extend([每页条数, 偏移量])

        # 获取总数
        总数结果 = await 异步连接池实例.执行查询(
            总数查询, tuple(查询参数[:-2])
        )  # 总数查询不需要LIMIT参数
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 获取数据列表
        账号列表 = await 异步连接池实例.执行查询(数据查询, tuple(查询参数))

        # 为每个账号查询好友数量
        好友数查询 = "SELECT COUNT(*) as count FROM 微信好友表 WHERE 我方微信号id = $1"
        for 账号 in 账号列表:
            好友数结果 = await 异步连接池实例.执行查询(好友数查询, (账号["id"],))
            账号["好友数量"] = 好友数结果[0]["count"] if 好友数结果 else 0

        # 转换为字典格式
        账号数据 = []
        for 账号 in 账号列表:
            账号数据.append(
                {
                    "微信id": 账号["id"],
                    "微信号": 账号["微信号"],
                    "绑定时间": 账号["绑定时间"].isoformat()
                    if 账号["绑定时间"]
                    else None,
                    "状态": "正常" if 账号["状态"] == 1 else "禁用",
                    "状态值": 账号["状态"],
                    "备注": 账号["备注"] or "",
                    "微信号修改时间": 账号["修改时间"].isoformat()
                    if 账号["修改时间"]
                    else None,
                    "关联更新时间": 账号["关联更新时间"].isoformat()
                    if 账号["关联更新时间"]
                    else None,
                    "好友数量": 账号.get("好友数量", 0),
                }
            )

        系统日志器.info(f"获取用户微信账号列表成功，用户id: {用户id}，总数: {总数}")

        return {
            "status": 状态.通用.成功,
            "data": {
                "列表": 账号数据,
                "总数": 总数,
                "页码": 页码,
                "每页条数": 每页条数,
                "总页数": (总数 + 每页条数 - 1) // 每页条数,
            },
        }

    except Exception as e:
        错误日志器.error(f"获取微信账号列表失败: {str(e)}")
        return {
            "status": 状态.通用.服务器错误,
            "message": f"获取微信账号列表失败: {str(e)}",
        }


# 原异步添加微信账号服务已合并到微信用户关联服务的异步绑定用户微信服务中
# 删除重复代码，统一使用绑定服务

# ================== 微信好友管理服务 ==================


async def 异步查询微信好友消息详情服务(
    用户id: int, 我方微信号id: int, 识别id: int
) -> Dict[str, Any]:
    """
    查询指定微信好友的消息时间详情信息

    Args:
        用户id: 当前登录用户的ID
        我方微信号id: 我方微信号id
        识别id: 微信好友识别id

    Returns:
        包含好友消息详情信息的字典
    """
    try:
        系统日志器.info(
            f"开始查询微信好友消息详情，用户id: {用户id}, 我方微信号id: {我方微信号id}, 识别id: {识别id}"
        )

        # 参数验证
        if error := _验证用户id(用户id):
            return error
        if error := _验证微信号id(我方微信号id, "我方微信号id"):
            return error
        if not isinstance(识别id, int) or 识别id <= 0:
            return {
                "status": 状态.通用.参数错误,
                "message": "识别id必须是正整数",
            }

        # 调用数据访问层查询
        查询结果 = await 异步数据_查询微信好友消息详情(用户id, 我方微信号id, 识别id)

        if 查询结果["status"] == 状态.通用.成功:
            系统日志器.info(
                f"成功查询微信好友消息详情，用户id: {用户id}, 我方微信号id: {我方微信号id}, 识别id: {识别id}"
            )

        return 查询结果

    except Exception as e:
        错误日志器.error(f"查询微信好友消息详情失败: {str(e)}")
        return {
            "status": 状态.通用.服务器错误,
            "message": f"查询微信好友消息详情失败: {str(e)}",
        }


async def 异步更新微信好友下次沟通时间服务(
    用户id: int,
    我方微信号id: int,
    识别id: int,
    下次沟通时间: Optional[datetime] = None,
) -> Dict[str, Any]:
    """
    更新微信好友表中的"下次沟通时间"字段

    Args:
        用户id: 当前用户id
        我方微信号id: 我方微信号id
        识别id: 识别id，用于定位具体的好友记录
        下次沟通时间: 指定的下次沟通时间，默认为None

    Returns:
        Dict[str, Any]: 包含status、message和data的标准响应格式
    """
    try:
        # 参数验证
        if error := _验证用户id(用户id):
            return error
        if error := _验证微信号id(我方微信号id, "我方微信号id"):
            return error

        if not 识别id:
            return {
                "status": 状态.通用.参数错误,
                "message": "识别id为必填参数",
            }

        系统日志器.info(
            f"开始更新微信好友下次沟通时间，用户id: {用户id}, 我方微信号id: {我方微信号id}"
        )

        # 验证用户权限并获取记录信息
        权限验证结果 = await 异步数据_验证用户微信好友权限(
            用户id, 我方微信号id, 识别id
        )

        if 权限验证结果["status"] != 状态.通用.成功:
            return 权限验证结果

        记录信息 = 权限验证结果["data"]["记录信息"]
        查询条件 = 权限验证结果["data"]["查询条件"]
        查询参数 = 权限验证结果["data"]["查询参数"]

        # 计算下次沟通时间
        if 下次沟通时间 is None:
            # 自动计算逻辑：基于最后消息时间 + 2小时
            我方最后消息时间 = 记录信息.get("我方最后一条消息发送时间")
            对方最后消息时间 = 记录信息.get("对方最后一条消息发送时间")
            现有下次沟通时间 = 记录信息.get("下次沟通时间")

            # 确定参考时间（最近的消息时间）
            参考时间 = None
            if 我方最后消息时间 and 对方最后消息时间:
                参考时间 = max(我方最后消息时间, 对方最后消息时间)
            elif 我方最后消息时间:
                参考时间 = 我方最后消息时间
            elif 对方最后消息时间:
                参考时间 = 对方最后消息时间

            # 检查是否需要更新下次沟通时间
            if 现有下次沟通时间 and 参考时间:
                # 如果现有下次沟通时间大于参考时间，则不更新
                if 现有下次沟通时间 > 参考时间:
                    return {
                        "status": 状态.通用.成功,
                        "message": "下次沟通时间无需更新（现有时间已是最新）",
                        "data": {
                            "下次沟通时间": 现有下次沟通时间.strftime(
                                "%Y-%m-%d %H:%M:%S"
                            )
                        },
                    }

            # 计算新的下次沟通时间
            if 参考时间:
                下次沟通时间 = 参考时间 + timedelta(hours=2)
            else:
                下次沟通时间 = datetime.now() + timedelta(hours=2)

        # 确保下次沟通时间不为None
        if 下次沟通时间 is None:
            下次沟通时间 = datetime.now() + timedelta(hours=2)

        # 调用数据访问层更新
        更新结果 = await 异步数据_更新微信好友下次沟通时间(
            查询条件, 查询参数, 下次沟通时间
        )

        if 更新结果["status"] == 状态.通用.成功:
            系统日志器.info(
                f"微信好友下次沟通时间更新成功，用户id: {用户id}, 下次沟通时间: {下次沟通时间}"
            )

        return 更新结果

    except Exception as e:
        错误日志器.error(f"更新微信好友下次沟通时间失败: {str(e)}")
        return {
            "status": 状态.通用.失败,
            "message": f"更新失败: {str(e)}",
        }


async def 异步获取微信好友列表服务(
    用户id: int,
    微信账号id: Optional[int] = None,
    页码: int = 1,
    每页条数: int = 20,
    好友类型: Optional[str] = None,
    关键词: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取用户微信号的好友列表

    Args:
        用户id: 用户标识，用于验证微信号所有权
        微信账号id: 微信账号id，如果不指定则获取用户所有微信号的好友
        页码: 页码，默认为1
        每页条数: 每页显示条数，默认为20
        好友类型: 好友类型筛选条件
        关键词: 搜索关键词，支持对方微信号搜索

    Returns:
        包含好友列表和分页信息的字典

    Note:
        1. 先验证微信号是否属于该用户
        2. 通过微信好友表获取好友关系
        3. 关联微信信息表获取好友的微信号信息
    """
    try:
        # 1. 获取用户拥有的微信号列表
        用户微信号查询 = """
        SELECT uwx.微信id
        FROM 用户微信关联表 uwx
        WHERE uwx.用户id = $1 AND uwx.状态 = 1
        """
        用户微信号列表结果 = await 异步连接池实例.执行查询(用户微信号查询, (用户id,))

        if not 用户微信号列表结果:
            return {"status": 状态.通用.失败, "message": "该用户没有关联的微信号"}

        用户微信id列表 = [row["微信id"] for row in 用户微信号列表结果]

        # 2. 构建查询条件
        查询参数 = []
        查询条件_列表 = []
        参数索引 = 1

        # 微信号筛选
        if 微信账号id:
            if 微信账号id not in 用户微信id列表:
                return {
                    "status": 状态.通用.失败,
                    "message": "指定的微信号不属于该用户",
                }
            查询条件_列表.append(f"f.我方微信号id = ${参数索引}")
            查询参数.append(微信账号id)
            参数索引 += 1
        else:
            # 获取用户所有微信号的好友
            if len(用户微信id列表) == 1:
                查询条件_列表.append(f"f.我方微信号id = ${参数索引}")
                查询参数.append(用户微信id列表[0])
                参数索引 += 1
            else:
                微信id占位符 = ",".join(
                    [f"${参数索引 + i}" for i in range(len(用户微信id列表))]
                )
                查询条件_列表.append(f"f.我方微信号id IN ({微信id占位符})")
                查询参数.extend(用户微信id列表)
                参数索引 += len(用户微信id列表)

        # 好友类型筛选
        if 好友类型:
            查询条件_列表.append(f"f.好友类型 = ${参数索引}")
            查询参数.append(好友类型)
            参数索引 += 1

        # 关键词搜索
        if 关键词:
            查询条件_列表.append(f"w.微信号 LIKE ${参数索引}")
            查询参数.append(f"%{关键词}%")
            参数索引 += 1

        查询条件 = "WHERE " + " AND ".join(查询条件_列表) if 查询条件_列表 else ""

        # 3. 获取总数
        总数查询 = f"""
        SELECT COUNT(*) as total 
        FROM 微信好友表 f 
        INNER JOIN 微信信息表 w ON f.对方微信号id = w.id 
        {查询条件}
        """

        # 4. 获取分页数据
        偏移量 = (页码 - 1) * 每页条数
        数据查询 = f"""
        SELECT f.id, f.我方微信号id, f.对方微信号id, f.好友关系,
               COALESCE(f.好友通过时间, f.发送请求时间, f.好友入库时间, f.创建时间) as 添加时间,
               w.微信号 as 对方微信号, f.备注
        FROM 微信好友表 f
        INNER JOIN 微信信息表 w ON f.对方微信号id = w.id
        {查询条件}
        ORDER BY COALESCE(f.好友通过时间, f.发送请求时间, f.好友入库时间, f.创建时间) DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        查询参数_分页 = 查询参数.copy()
        查询参数_分页.extend([每页条数, 偏移量])

        # 获取总数
        总数结果 = await 异步连接池实例.执行查询(总数查询, tuple(查询参数))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 获取数据列表
        好友列表 = await 异步连接池实例.执行查询(数据查询, tuple(查询参数_分页))

        # 转换为字典格式
        好友数据 = []
        for 好友 in 好友列表:
            好友数据.append(
                {
                    "好友ID": 好友["id"],
                    "我方微信号id": 好友["我方微信号id"],
                    "对方微信号id": 好友["对方微信号id"],
                    "对方微信号": 好友["对方微信号"],
                    "好友关系": 好友["好友关系"],
                    "添加时间": 好友["添加时间"].isoformat()
                    if 好友["添加时间"]
                    else None,
                    "备注": 好友["备注"] or "",
                }
            )

        系统日志器.info(f"获取用户微信好友列表成功，用户id: {用户id}，总数: {总数}")

        return {
            "status": 状态.通用.成功,
            "data": {
                "列表": 好友数据,
                "总数": 总数,
                "页码": 页码,
                "每页条数": 每页条数,
                "总页数": (总数 + 每页条数 - 1) // 每页条数,
            },
        }

    except Exception as e:
        错误日志器.error(f"获取微信好友列表失败: {str(e)}")
        return {
            "status": 状态.通用.服务器错误,
            "message": f"获取微信好友列表失败: {str(e)}",
        }





# ================== 微信统计数据服务 ==================


# ================== 辅助函数 ==================





# ================== 辅助服务函数 ==================

# 注意：已删除有语法错误的异步更新微信账号关联服务函数
# 如需实现微信账号关联更新功能，需要重新编写正确的函数


# ================== 补全缺失的微信账号管理服务函数 ==================


async def 异步更新微信账号服务(
    用户id: int,
    账号id: int,
    昵称: Optional[str] = None,
    微信号: Optional[str] = None,
    绑定手机号: Optional[str] = None,
    头像: Optional[str] = None,
    备注: Optional[str] = None,
    状态: Optional[int] = None,
):
    """
    更新微信账号信息

    Args:
        用户id: 用户标识
        账号id: 微信账号id
        昵称: 微信昵称
        微信号: 微信号
        绑定手机号: 绑定的手机号
        头像: 头像URL
        备注: 备注信息
        状态: 账号状态

    Returns:
        包含更新结果的字典
    """
    try:
        # 检查用户是否有权限更新该微信账号
        检查权限查询 = """
        SELECT uwx.id FROM 用户微信关联表 uwx
        INNER JOIN 微信信息表 wx ON uwx.微信id = wx.id
        WHERE uwx.用户id = $1 AND wx.id = $2
        """
        权限结果 = await 异步连接池实例.执行查询(检查权限查询, (用户id, 账号id))

        if not 权限结果:
            return 统一响应模型.失败(428, "无权限更新该微信账号")

        # 更新微信信息表
        更新字段列表 = []
        更新参数列表 = []
        参数索引 = 1

        if 昵称 is not None:
            更新字段列表.append(f"昵称 = ${参数索引}")
            更新参数列表.append(昵称)
            参数索引 += 1

        if 微信号:
            更新字段列表.append(f"微信号 = ${参数索引}")
            更新参数列表.append(微信号)
            参数索引 += 1

        if 绑定手机号 is not None:
            更新字段列表.append(f"绑定手机号 = ${参数索引}")
            更新参数列表.append(绑定手机号)
            参数索引 += 1

        if 头像 is not None:
            更新字段列表.append(f"头像 = ${参数索引}")
            更新参数列表.append(头像)
            参数索引 += 1

        if 更新字段列表:
            更新微信查询 = f"UPDATE 微信信息表 SET {', '.join(更新字段列表)} WHERE id = ${参数索引}"
            更新参数列表.append(账号id)
            await 异步连接池实例.执行更新(更新微信查询, tuple(更新参数列表))

        # 更新用户微信关联表
        关联更新字段列表 = []
        关联更新参数列表 = []
        关联参数索引 = 1

        if 备注 is not None:
            关联更新字段列表.append(f"备注 = ${关联参数索引}")
            关联更新参数列表.append(备注)
            关联参数索引 += 1

        if 状态 is not None:
            关联更新字段列表.append(f"状态 = ${关联参数索引}")
            关联更新参数列表.append(状态)
            关联参数索引 += 1

        if 关联更新字段列表:
            更新关联查询 = f"""
            UPDATE 用户微信关联表 SET {", ".join(关联更新字段列表)}
            WHERE 用户id = ${关联参数索引} AND 微信id = ${关联参数索引 + 1}
            """
            关联更新参数列表.extend([用户id, 账号id])
            await 异步连接池实例.执行更新(更新关联查询, tuple(关联更新参数列表))

        系统日志器.info(f"成功更新微信账号，用户id: {用户id}，账号id: {账号id}")

        return 统一响应模型.成功(消息="更新微信账号成功")

    except Exception as e:
        错误日志器.error(f"更新微信账号失败: {str(e)}")
        return 统一响应模型.失败(500, f"更新微信账号失败: {str(e)}")






async def 异步服务_通过识别id更新用户微信好友信息(
    用户id: int,
    我方微信id: int,
    好友识别id: str,
    是否失效: Optional[int] = None,
    发送请求时间: Optional[datetime] = None,
    好友入库时间: Optional[datetime] = None,
    我方最后一条消息发送时间: Optional[datetime] = None,
    对方最后一条消息发送时间: Optional[datetime] = None,
    下次沟通时间: Optional[datetime] = None,
    备注: Optional[str] = None,
):
    """
    服务层：通过识别id更新用户微信好友信息

    Args:
        用户id: 用户标识
        我方微信id: 我方微信账号id
        好友识别id: 好友识别id，用于定位记录
        是否失效: 好友是否失效（0：有效，1：失效）
        发送请求时间: 发送好友请求的时间
        好友入库时间: 好友入库的时间
        我方最后一条消息发送时间: 我方最后发送消息的时间
        对方最后一条消息发送时间: 对方最后发送消息的时间
        下次沟通时间: 下次沟通时间
        备注: 备注信息

    Returns:
        包含操作结果的字典
    """
    try:
        # 1. 验证用户对微信账号的权限
        关联信息 = await 异步数据_查询用户微信关联信息(用户id, 我方微信id)
        if not 关联信息:
            # 检查微信号是否存在
            微信号信息 = await 异步数据_查询微信号信息(我方微信id)
            if not 微信号信息:
                return {
                    "status": 状态.通用.不存在,
                    "message": f"微信号id {我方微信id} 不存在",
                    "data": None,
                }
            else:
                return {
                    "status": 状态.微信.获取用户绑定账号失败,
                    "message": f"微信号 {微信号信息['微信号']} 不属于当前用户，无权限访问",
                    "data": None,
                }

        # 2. 检查是否有需要更新的字段
        更新字段映射 = {
            "是否失效": 是否失效,
            "发送请求时间": 发送请求时间,
            "好友入库时间": 好友入库时间,
            "我方最后一条消息发送时间": 我方最后一条消息发送时间,
            "对方最后一条消息发送时间": 对方最后一条消息发送时间,
            "下次沟通时间": 下次沟通时间,
            "备注": 备注,
        }

        # 过滤掉值为None的字段
        有效更新字段 = {k: v for k, v in 更新字段映射.items() if v is not None}

        if not 有效更新字段:
            return {
                "status": 状态.通用.参数错误,
                "message": "没有提供需要更新的字段",
                "data": None,
            }

        # 3. 调用数据层执行更新
        更新结果 = await 异步数据_通过识别id更新微信好友信息(
            我方微信id=我方微信id, 好友识别id=好友识别id, 更新字段=有效更新字段
        )


        if 更新结果.get("影响行数", 0) > 0:
            return 统一响应模型.成功(
                数据={
                    "影响行数": 更新结果.get("影响行数"),
                    "更新字段数": len(有效更新字段),
                },
                消息="微信好友信息更新成功"
            )
        else:
            return 统一响应模型.失败(
                状态.微信.好友未入库,
                "更新失败，没有找到对应的好友记录或记录未发生变化"
            )

    except Exception as e:
        错误日志器.error(f"服务_通过识别id更新用户微信好友信息失败: {str(e)}")
        return 统一响应模型.失败(状态.通用.服务器错误, f"更新微信好友信息失败: {str(e)}")





# ================== 补全微信消息管理服务函数 ==================


async def 异步服务_获取指定用户的微信账号和好友统计概览数据(
    用户id: int,
) -> Dict[str, Any]:
    """
    获取用户的微信统计概览，包括绑定的微信账号总数、好友总数和对接进度统计。

    Args:
        用户id: 要查询的用户id。

    Returns:
        一个包含统计数据的字典。
    """
    try:
        # 查询绑定的微信账号总数
        账号总数查询 = "SELECT COUNT(*) as total FROM 用户微信关联表 WHERE 用户id = $1"
        账号总数结果 = await 异步连接池实例.执行查询(账号总数查询, (用户id,))
        微信账号总数 = 账号总数结果[0]["total"] if 账号总数结果 else 0

        # 查询好友总数
        好友总数查询 = """
            SELECT COUNT(*) as total
            FROM 微信好友表 f
            JOIN 用户微信关联表 uwx ON f.我方微信号id = uwx.微信id
            WHERE uwx.用户id = $1
        """
        好友总数结果 = await 异步连接池实例.执行查询(好友总数查询, (用户id,))
        好友总数 = 好友总数结果[0]["total"] if 好友总数结果 else 0

        # 查询对接进度统计
        对接进度统计查询 = """
            SELECT
                COUNT(*) as 总对接数,
                COUNT(CASE WHEN 开播状态 > 0 THEN 1 END) as 成功对接数,
                COUNT(CASE WHEN 开播状态 = 0 AND (意向状态 = 1 OR 样品状态 > 0 OR 排期状态 > 0) THEN 1 END) as 进行中对接数
            FROM 微信产品对接进度表
            WHERE 用户id = $1
        """
        对接进度结果 = await 异步连接池实例.执行查询(对接进度统计查询, (用户id,))

        if 对接进度结果:
            进行中对接 = 对接进度结果[0]["进行中对接数"] or 0
            成功对接 = 对接进度结果[0]["成功对接数"] or 0
        else:
            进行中对接 = 0
            成功对接 = 0

        统计数据 = {
            "微信账号总数": 微信账号总数,
            "好友总数": 好友总数,
            "进行中对接": 进行中对接,
            "成功对接": 成功对接,
        }

        return 统计数据

    except Exception as e:
        错误日志器.error(
            f"获取用户微信统计概览失败，用户id: {用户id} - {e}", exc_info=True
        )
        # 注意：在路由层已经包装了统一响应模型，这里直接返回异常或None
        raise e


async def 异步更新微信头像服务(
    用户id: int, 微信号: str, 微信头像: str
):
    """
    更新微信头像服务

    更新微信信息表中指定微信号的头像字段，支持base64格式的头像数据

    Args:
        用户id (int): 用户id，用于权限验证
        微信号 (str): 要更新头像的微信号
        微信头像 (str): base64编码的头像数据

    Returns:
        Dict[str, Any]: 包含状态和消息的字典
    """
    try:
        系统日志器.info(f"开始更新微信头像，用户id: {用户id}, 微信号: {微信号}")

        # 1. 验证微信号是否属于该用户
        验证SQL = """
        SELECT uwx.微信id, wx.微信号
        FROM 用户微信关联表 uwx
        INNER JOIN 微信信息表 wx ON uwx.微信id = wx.id
        WHERE uwx.用户id = $1 AND wx.微信号 = $2 AND uwx.状态 = 1
        """
        验证结果 = await 异步连接池实例.执行查询(验证SQL, [用户id, 微信号])

        if not 验证结果:
            return 统一响应模型.失败(状态.通用.失败, "该微信账号不属于当前用户或微信号不存在")

        微信id = 验证结果[0]["微信id"]

        # 2. 计算头像数据大小
        头像大小 = len(微信头像.encode("utf-8"))

        # 3. 更新微信头像
        更新SQL = """
        UPDATE 微信信息表
        SET 微信头像 = $1, 修改时间 = CURRENT_TIMESTAMP
        WHERE id = $2
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, [微信头像, 微信id])

        if 影响行数 > 0:
            # 4. 获取更新时间
            查询时间SQL = "SELECT 修改时间 FROM 微信信息表 WHERE id = $1"
            时间结果 = await 异步连接池实例.执行查询(查询时间SQL, [微信id])
            更新时间 = (
                时间结果[0]["修改时间"].strftime("%Y-%m-%d %H:%M:%S")
                if 时间结果
                else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

            系统日志器.info(
                f"微信头像更新成功，微信号: {微信号}, 头像大小: {头像大小} 字节"
            )

            return 统一响应模型.成功(
                数据={"微信号": 微信号, "更新时间": 更新时间, "头像大小": 头像大小},
                消息="微信头像更新成功"
            )
        else:
            return 统一响应模型.失败(状态.通用.失败, "更新失败，没有记录被修改")

    except Exception as e:
        错误日志器.error(f"更新微信头像失败: {str(e)}")
        return 统一响应模型.失败(状态.通用.失败, f"更新微信头像失败: {str(e)}")


async def 异步查询用户微信好友请求状态业务服务(
    用户id: int, 微信信息表id: int
):
    """
    查询用户微信好友请求状态业务服务层

    纯业务逻辑层，负责协调数据访问层完成业务流程
    不直接操作数据库，通过数据访问层完成所有数据库操作

    业务流程：
    1. 查询是否有好友请求状态不为null的记录
    2. 如果有，返回完整信息
    3. 如果没有，查询未完成微信添加的记录
    4. 如果需要创建关联记录，则创建
    5. 如果所有记录都已处理完成，提示需要补充达人联系方式

    Args:
        用户id: 用户id
        微信信息表id: 微信信息表id（必填，用于多微信账号场景）

    Returns:
        Dict[str, Any]: 包含状态和数据的统一响应格式
    """
    try:
        系统日志器.info(
            f"开始查询用户微信好友请求状态: 用户id={用户id}, 微信信息表id={微信信息表id}"
        )

        # 第一步：查询是否有好友请求状态为空或为0的记录
        待处理微信添加记录 = await 用户微信好友请求状态数据访问层.查询用户微信添加记录表中好友请求状态为空或为0的记录(
            用户id, 微信信息表id
        )

        if 待处理微信添加记录:
            # 有待处理的记录，获取详细信息
            达人详细信息 = await 用户微信好友请求状态数据访问层.根据用户达人补充信息表id查询达人详细信息(
                待处理微信添加记录["用户达人补充信息表id"]
            )

            if 达人详细信息:
                系统日志器.info(
                    f"找到待处理的好友请求记录: 用户id={用户id}, 微信添加记录id={待处理微信添加记录['微信添加记录id']}"
                )
                return 统一响应模型.成功({
                    "状态类型": "需要添加好友请求",
                    "微信添加记录id": 待处理微信添加记录["微信添加记录id"],
                    "达人昵称": 达人详细信息.get("达人昵称"),
                    "联系方式": 达人详细信息.get("联系方式"),
                    "联系方式类型": 达人详细信息.get("联系方式类型"),
                    "用户达人补充信息表id": 达人详细信息["用户达人补充信息表id"],
                    "用户达人关联表id": 达人详细信息["用户达人关联表id"],
                    "好友请求状态": 待处理微信添加记录["好友请求状态"],
                }, "查询微信好友请求状态成功")

        # 第二步：查询未完成微信添加的记录
        未完成微信添加记录 = await 用户微信好友请求状态数据访问层.查询用户达人补充信息表中未完成微信添加的记录(
            用户id, 微信信息表id
        )

        if 未完成微信添加记录:
            # 检查是否需要创建关联记录
            if 未完成微信添加记录["现有微信添加记录id"] is None:
                # 没有关联，需要创建关联记录（但不在这里创建，而是返回标识让上层处理）
                系统日志器.info(f"检测到需要创建微信添加记录: 用户id={用户id}")
                return 统一响应模型.成功({
                    "状态类型": "需要添加好友请求",
                    "微信添加记录id": None,  # 标识需要创建
                    "达人昵称": 未完成微信添加记录.get("达人昵称"),
                    "联系方式": 未完成微信添加记录.get("联系方式"),
                    "联系方式类型": 未完成微信添加记录.get("联系方式类型"),
                    "用户达人补充信息表id": 未完成微信添加记录[
                        "用户达人补充信息表id"
                    ],
                    "用户达人关联表id": 未完成微信添加记录["用户达人关联表id"],
                    "好友请求状态": None,
                }, "需要创建微信添加记录，需要添加好友请求")
            else:
                # 有关联但好友请求状态为null，返回该记录
                系统日志器.info(
                    f"找到未完成的微信添加记录: 用户id={用户id}, 记录ID={未完成微信添加记录['现有微信添加记录id']}"
                )
                return 统一响应模型.成功({
                    "状态类型": "需要添加好友请求",
                    "微信添加记录id": 未完成微信添加记录["现有微信添加记录id"],
                    "达人昵称": 未完成微信添加记录.get("达人昵称"),
                    "联系方式": 未完成微信添加记录.get("联系方式"),
                    "联系方式类型": 未完成微信添加记录.get("联系方式类型"),
                    "用户达人补充信息表id": 未完成微信添加记录[
                        "用户达人补充信息表id"
                    ],
                    "用户达人关联表id": 未完成微信添加记录["用户达人关联表id"],
                    "好友请求状态": None,
                }, "需要添加好友请求")

        # 第三步：所有记录都已处理完成，需要补充达人联系方式
        系统日志器.info(
            f"用户所有达人记录都已处理完成，需要补充达人联系方式: 用户id={用户id}"
        )
        return 统一响应模型.成功({
            "状态类型": "需要补充达人联系方式",
            "微信添加记录id": None,
            "达人昵称": None,
            "联系方式": None,
            "联系方式类型": None,
            "用户达人补充信息表id": None,
            "用户达人关联表id": None,
            "好友请求状态": None,
        }, "需要补充达人联系方式")

    except Exception as e:
        错误日志器.error(
            f"查询用户微信好友请求状态业务服务异常: 用户id={用户id}, 错误={str(e)}"
        )
        return 统一响应模型.失败(状态.微信.获取用户好友失败, f"查询微信好友请求状态失败: {str(e)}")


# ================== 微信好友下次沟通时间管理服务 ==================


async def 异步获取微信好友下次沟通时间列表服务(
    用户id: int,
    我方微信号id: int,
):
    """
    获取所有好友中下次沟通时间最小的那条记录服务

    从微信好友表中查询并返回下次沟通时间最小的好友数据（包括null值优先）

    Args:
        用户id: 用户标识，用于验证微信号所有权和权限控制
        我方微信号id: 我方微信号id

    Returns:
        Dict[str, Any]: 包含下次沟通时间最小的好友信息的标准响应格式

    Note:
        1. 返回所有好友中下次沟通时间最小的那条记录，null值优先
        2. 包含好友基本信息、沟通时间、最后消息时间等关键字段
        3. 支持用户权限验证，确保只能查看自己的微信好友
    """
    try:
        # 参数验证
        if error := _验证用户id(用户id):
            return error

        if error := _验证微信号id(我方微信号id, "我方微信号id"):
            return error

        系统日志器.info(
            f"开始获取下次沟通时间最小的微信好友信息，用户id: {用户id}, 我方微信号id: {我方微信号id}"
        )

        # 权限验证：验证微信号是否属于当前用户
        关联信息 = await 异步数据_查询用户微信关联信息(用户id, 我方微信号id)
        if not 关联信息:
            # 检查微信号是否存在
            微信号信息 = await 异步数据_查询微信号信息(我方微信号id)
            if not 微信号信息:
                return 统一响应模型.失败(状态.通用.不存在, f"微信号id {我方微信号id} 不存在")
            else:
                return 统一响应模型.失败(
                    状态.微信.获取用户绑定账号失败,
                    f"微信号 {微信号信息['微信号']} 不属于当前用户，无权限访问"
                )

        # 调用数据访问层查询
        查询结果 = await 异步数据_获取微信好友下次沟通时间列表(我方微信号id)

        if 查询结果["status"] != 状态.通用.成功:
            if 查询结果["status"] == 状态.通用.不存在:
                # 获取微信号信息用于更明确的提示
                return 统一响应模型.失败(
                    状态.通用.不存在,
                    f"微信号 {关联信息['微信号']}({关联信息['昵称']}) 暂无好友记录"
                )
            else:
                return 统一响应模型.失败(查询结果["status"], 查询结果["message"])

        # 格式化返回数据
        好友数据 = 查询结果["data"]
        下次沟通时间 = 好友数据["下次沟通时间"]

        # 获取上海时区的当前时间，与数据库时区保持一致
        上海时区 = pytz.timezone('Asia/Shanghai')
        当前时间 = datetime.now(上海时区)

        # 如果下次沟通时间没有时区信息，假设它是上海时区的时间
        if 下次沟通时间 and 下次沟通时间.tzinfo is None:
            下次沟通时间 = 上海时区.localize(下次沟通时间)
        elif 下次沟通时间 and 下次沟通时间.tzinfo:
            # 如果有时区信息，转换为上海时区
            下次沟通时间 = 下次沟通时间.astimezone(上海时区)

        # 判断是否到了沟通时间：null值或时间已到都算到了
        沟通时间到了 = 下次沟通时间 is None or 下次沟通时间 <= 当前时间

        返回数据 = {
            "微信id": 好友数据["对方微信号id"],
            "识别id": 好友数据["识别id"],
            "下次沟通时间": 好友数据["下次沟通时间"].strftime("%Y-%m-%d %H:%M:%S")
            if 好友数据["下次沟通时间"]
            else None,
            "到了没": 沟通时间到了,
        }

        系统日志器.info(
            f"成功获取下次沟通时间最小的微信好友信息，用户id: {用户id}, 好友识别id: {好友数据['识别id']}"
        )

        return 统一响应模型.成功(返回数据, "OK!成功了")

    except Exception as e:
        错误日志器.error(f"获取下次沟通时间最小的微信好友信息失败: {str(e)}")
        return 统一响应模型.失败(状态.微信.获取好友列表失败, f"获取下次沟通时间最小的微信好友信息失败: {str(e)}")
