# 导入统一日志系统


class 用户:
    密码错误 = 401
    用户不存在 = 402
    令牌无效 = 403
    令牌过期 = 404
    没有权限 = 405
    旧密码不正确 = 406
    密码太短 = 407
    新密码强度不足 = 408
    密码更新失败 = 409
    验证码错误 = 410
    手机号格式错误 = 411
    手机号已存在 = 412
    注册失败 = 413
    重置密码失败 = 414
    重置密码成功 = 415
    重置密码验证码错误 = 416
    重置密码验证码过期 = 417
    重置密码验证码发送失败 = 418
    重置密码验证码发送成功 = 419
    权限已过期 = 420
    用户状态获取失败 = 421
    店铺状态获取失败 = 422
    店铺名称获取失败 = 423
    店铺头像获取失败 = 424
    加入店铺失败 = 425
    关联店铺失败 = 426
    参数缺失 = 427
    权限不足 = 428
    登录认证失败 = 429
    更新用户信息失败 = 430
    昵称未变更 = 431
    # 新增账户状态相关状态码
    账户未激活 = 432
    账户被禁用 = 433
    账户状态异常 = 434


class 激活:
    激活码不存在 = 103  # 使用通用.不存在
    激活码已被本人使用 = 104  # 使用通用.已存在
    激活码已被他人使用 = 104  # 使用通用.已存在
    激活码类型不存在 = 103  # 使用通用.不存在
    激活类型未绑定权限 = 103  # 使用通用.参数错误
    激活类型未绑定时间 = 103  # 使用通用.参数错误
    激活用户不存在 = 103  # 使用通用.不存在
    激活成功 = 100  # 使用通用.成功
    激活失败 = 101  # 使用通用.失败
    已有邀请人 = 104  # 使用通用.已存在


class 通用:
    注册成功 = 100
    数据库错误 = 102
    接口已废弃 = 1001
    # 你可以在这里继续定义其他状态
    参数错误 = 103
    获取权限成功 = 1
    成功_旧 = 0
    无权限 = 104
    成功 = 100
    失败 = 101
    部分成功 = 102
    服务器错误 = 500
    生成令牌失败 = 501
    令牌已过期 = 502
    令牌无效 = 503
    令牌解析失败 = 504
    令牌缺失 = 505
    令牌内容无效 = 506
    不存在 = 103
    已存在 = 104
    未找到 = 404
    # 新增状态
    无数据 = 204
    数据不存在 = 404
    超出限制 = 429
    未授权 = 401
    操作失败 = 500
    服务暂时不可用 = 503
    权限不足 = 403


class 注册:
    手机号已存在 = 101
    验证码错误 = 102
    注册成功 = 100
    手机号格式错误 = 104
    注册失败 = 105
    设置权限失败 = 106


class 短信服务:
    发送失败 = 503001
    验证码错误 = 400101
    验证码过期 = 400102


class 邀约:
    邀约成功 = 1001
    邀约失败 = 1300  # 通用邀约失败
    没有权限 = 1301  # 用户没有邀约权限（例如基础权限id=1过期）
    权限过期 = 1302  # 用户的邀约权限已过期
    请求参数错误 = 1303  # 邀约时，KOL信息等参数错误
    # 新增状态码
    基础权限不足或过期 = 1304  # 细化权限不足的原因
    免费次数超限 = 1305  # 免费用户达到每日邀约上限
    更新KOL数据失败 = 1306  # 调用内部服务更新KOL信息失败
    用户不存在_内部 = 1307  # 用于日志，表示发起邀约的用户在系统中不存在
    请求参数错误_内部 = 1308  # 用于日志，表示邀约时内部参数校验失败
    失败_系统错误 = 1309  # 邀约过程中发生未捕获的系统级错误
    成功 = 100  # 使用通用成功


class 订单:
    订单创建成功 = 1001
    订单创建失败 = 1002
    订单查询失败 = 1003
    订单不存在 = 1004
    订单已支付 = 1005
    订单已取消 = 1006
    订单已过期 = 1007
    订单已退款 = 1008
    订单已关闭 = 1009
    订单回调数据错误 = 1010
    计划不存在 = 1011
    # 新增支付相关状态码
    支付功能不可用 = 1012
    支付链接生成失败 = 1013
    支付通知验证失败 = 1014
    支付通知格式错误 = 1015
    支付通知处理失败 = 1016
    参数错误 = 1017
    订单状态错误 = 1018


class AI:
    次数不足 = 1001
    服务不可用 = 1002
    服务超时 = 1003
    服务异常 = 1004
    配置到期 = 1005
    配置不存在 = 1006
    会话不存在 = 1007
    会话清除成功 = 1008
    会话清除失败 = 1009
    会话清除异常 = 1010
    会话清除结果 = 1011
    会话清除参数错误 = 1012
    会话清除未知错误 = 1013
    次数超限 = 1014
    成功 = 100
    参数无效 = 1015


class AI模型:
    获取模型市场成功 = 100
    获取模型市场失败 = 2002
    获取模型详情成功 = 100
    获取模型详情失败 = 2004
    模型不存在 = 2005
    创建智能体成功 = 100
    创建智能体失败 = 2007
    已创建智能体 = 2008
    创建智能体失败 = 2009
    创建知识库失败 = 2010
    发布智能体失败 = 2011
    获取我的AI配置成功 = 100
    获取我的AI配置失败 = 2012
    获取我的AI配置详情成功 = 100
    获取我的AI配置详情失败 = 2014
    更新我的AI配置成功 = 100
    更新我的AI配置失败 = 2016
    用户AI信息不存在 = 2017
    用户AI信息不属于用户 = 2018
    参数错误 = 2019
    服务器错误 = 2020
    训练知识库成功 = 100
    训练知识库失败 = 2021
    知识库不存在 = 2022
    成功 = 100


class 团队邀请:
    成功 = 100  # 通用成功码
    邀请发送成功 = 3001
    邀请处理成功 = 3002

    # 失败状态码 3100-3199
    邀请发送失败 = 3101
    手机号格式错误 = 3102
    用户不存在 = 3103
    已是团队成员 = 3104
    邀请已存在 = 3105
    团队不存在 = 3106
    权限不足 = 3107
    邀请不存在 = 3108
    邀请已过期 = 3109
    邀请已处理 = 3110
    令牌无效 = 3111
    生成链接失败 = 3112
    撤销失败 = 3113
    重发失败 = 3114
    邀请操作失败 = 3115
    身份验证失败 = 3116

    # 系统错误 3200-3299
    系统错误 = 3201
    数据库错误 = 3202
    服务异常 = 3203


class 物流:
    查询成功但无物流信息 = 101
    查询失败 = 102


class 产品管理:
    # 成功状态码 1000-1099
    成功 = 100  # 通用成功码
    产品创建成功 = 1001
    产品更新成功 = 1002
    产品删除成功 = 1003
    产品列表获取成功 = 1004
    产品详情获取成功 = 1005
    产品解析成功 = 1006

    # 失败状态码 1100-1199
    产品创建失败 = 1101
    产品更新失败 = 1102
    产品删除失败 = 1103
    产品列表获取失败 = 1104
    产品详情获取失败 = 1105
    产品解析失败 = 1106
    产品不存在 = 1107
    产品无权限访问 = 1108
    产品名称重复 = 1109
    产品信息格式错误 = 1110
    产品解析超时 = 1111
    产品解析服务不可用 = 1112
    产品已提交到知识库 = 1113

    # 参数错误 1200-1299
    参数错误 = 1201
    产品名称为空 = 1202
    产品信息文本为空 = 1203
    产品id无效 = 1204
    分页参数错误 = 1205


class 微信:
    # 账号管理 3300-3399
    获取账号列表失败 = 3301
    添加账号失败 = 3302
    更新账号失败 = 3303

    # 好友管理 3400-3499
    获取好友列表失败 = 3401
    添加好友失败 = 3402
    更新好友失败 = 3403
    好友未入库 = 3405

    # 对接进度 3500-3599
    获取对接进度列表失败 = 3501
    创建对接进度失败 = 3502
    更新对接进度失败 = 3503
    删除对接进度失败 = 3504

    # 消息管理 3600-3699
    获取消息记录失败 = 3601
    发送消息失败 = 3602

    # 统计 3700-3799
    获取统计失败 = 3701
    获取好友统计失败 = 3702
    获取消息统计失败 = 3703
    获取概览失败 = 3704

    # 群组管理 3800-3899
    获取群列表失败 = 3801
    创建群失败 = 3802

    # 配置管理 3900-3999
    配置已存在 = 3901
    配置不存在 = 3902
    配置创建失败 = 3903
    配置更新失败 = 3904
    配置删除失败 = 3905
    更新群失败 = 3803

    # 关联管理 3900-3999
    获取用户绑定账号失败 = 3901
    获取用户好友失败 = 3902
    绑定账号失败 = 3903
    解绑账号失败 = 3904


class 线索搜索:
    # 搜索限制相关状态码 4000-4099
    搜索次数已达上限 = 4001
    非会员搜索次数已达上限 = 4002
    会员搜索次数已达上限 = 4003
    搜索权限检查失败 = 4004
    搜索记录更新失败 = 4005
    获取搜索次数失败 = 4006
    获取会员状态失败 = 4007

    # 翻页限制相关状态码 4100-4199
    翻页次数已达上限 = 4101
    翻页次数警告 = 4102
    翻页次数严重警告 = 4103
    翻页权限检查失败 = 4104
    翻页记录更新失败 = 4105
    获取翻页次数失败 = 4106


class 公司管理:
    # 公司管理相关状态码 5000-5099
    创建成功 = 5001
    创建失败 = 5002
    已有未审核公司 = 5003
    公司名称重复 = 5004
    公司代码重复 = 5005
    公司不存在 = 5006
    审核成功 = 5007
    审核失败 = 5008
    权限不足 = 5009
    参数错误 = 5010


class 团队管理:
    # 团队管理相关状态码 6000-6099
    创建成功 = 6001
    创建失败 = 6002
    创建数量已达上限 = 6003
    团队名称重复 = 6004
    团队不存在 = 6005
    权限不足 = 6006
    参数错误 = 6007
    解散成功 = 6008
    解散失败 = 6009


class 客户邀请:
    # 客户邀请相关状态码 7000-7099
    创建成功 = 7001
    创建失败 = 7002
    手机号已注册 = 7003
    邀请已存在 = 7004
    手机号格式错误 = 7005
    每日限制已达上限 = 7006
    邀请不存在 = 7007
    邀请已过期 = 7008
    邀请已处理 = 7009
    权限不足 = 7010
    参数错误 = 7011


class 达人管理:
    # 达人管理相关状态码 8000-8099
    搜索成功 = 100
    搜索失败 = 8002
    添加成功 = 100
    添加失败 = 8004
    已经认领 = 8005
    达人不存在 = 8006
    UID不能为空 = 8007
    抖音号不能为空 = 8008
    第三方搜索超时 = 8009
    第三方搜索服务错误 = 8010
    第三方搜索服务异常 = 8011
    未找到相关达人 = 8012
    查询或更新达人失败 = 8013
    建立用户关联失败 = 8014
    参数错误 = 8015


class 工具调用:
    # 工具调用相关状态码 9000-9299
    # 成功状态码 9000-9099
    调用成功 = 9000
    测试成功 = 9001
    注册成功 = 9002
    加载成功 = 9003
    初始化成功 = 9004

    # 失败状态码 9100-9199
    调用失败 = 9100
    工具不存在 = 9101
    参数错误 = 9102
    权限不足 = 9103
    超时错误 = 9104
    配置错误 = 9105
    注册失败 = 9106
    加载失败 = 9107
    初始化失败 = 9108
    频率限制 = 9109
    安全级别不足 = 9110
    系统繁忙 = 9111

    # MCP工具状态码 9200-9299
    MCP连接失败 = 9200
    MCP服务器不可用 = 9201
    MCP工具加载失败 = 9202
    MCP配置错误 = 9203
    MCP认证失败 = 9204


class LangChain:
    # LangChain相关状态码 1500-1599
    # 成功状态码 1500-1519
    操作成功 = 100  # 使用通用成功码
    # 成功状态统一使用100，这里只保留错误状态码

    # 失败状态码 1520-1599
    智能体创建失败 = 1501
    智能体更新失败 = 1502
    知识库创建失败 = 1503
    模型配置失败 = 1504
    检索测试失败 = 1505
    智能体不存在 = 1506
    知识库不存在 = 1507
    模型不存在 = 1508
    配置错误 = 1509
    权限不足 = 1510
    参数错误 = 1511
    数据验证失败 = 1512
    服务不可用 = 1513
    初始化失败 = 1514
    连接失败 = 1515
    超时错误 = 1516
    数据格式错误 = 1517
    文件上传失败 = 1518
    文件处理失败 = 1519
    向量化失败 = 1520
    获取检索配置失败 = 1521
    更新检索配置失败 = 1522
    删除操作失败 = 1523


# 将所有状态类组合在一个状态对象下
class 状态类:
    def __init__(self):
        self.用户 = 用户
        self.激活 = 激活
        self.通用 = 通用
        self.注册 = 注册
        self.短信服务 = 短信服务
        self.邀约 = 邀约
        self.订单 = 订单
        self.AI = AI
        self.AI模型 = AI模型
        self.团队邀请 = 团队邀请
        self.微信 = 微信
        self.线索搜索 = 线索搜索
        self.公司管理 = 公司管理
        self.团队管理 = 团队管理
        self.客户邀请 = 客户邀请
        self.达人管理 = 达人管理
        self.工具调用 = 工具调用
        self.LangChain = LangChain


# 创建状态实例，方便导入
状态 = 状态类()
