"""
团队管理功能相关的Pydantic模型
包含公司管理、团队管理、成员管理等相关的请求和响应模型
"""

from typing import List, Optional

from pydantic import BaseModel, Field

# =============== 公司管理模型 ===============


class 公司创建请求(BaseModel):
    公司名称: str = Field(..., min_length=1, max_length=255, description="公司名称")
    公司简称: Optional[str] = Field(None, max_length=100, description="公司简称")
    公司代码: Optional[str] = Field(None, max_length=50, description="公司代码")
    公司地址: Optional[str] = Field(None, description="公司地址")
    联系电话: Optional[str] = Field(None, max_length=50, description="联系电话")
    邮箱: Optional[str] = Field(None, max_length=255, description="公司邮箱")
    法人代表: Optional[str] = Field(None, max_length=100, description="法人代表")
    营业执照号: Optional[str] = Field(None, max_length=100, description="营业执照号")
    备注: Optional[str] = Field(None, description="备注信息")


class 公司列表请求(BaseModel):
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(10, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    公司状态: Optional[str] = Field(None, description="公司状态")
    审核状态: Optional[str] = Field(
        None, description="审核状态：待审核、已批准、已拒绝"
    )


# =============== 团队管理模型 ===============


class 团队创建请求(BaseModel):
    团队名称: str = Field(..., min_length=1, max_length=255, description="团队名称")
    公司id: int = Field(..., gt=0, description="所属公司id")
    团队代码: Optional[str] = Field(None, max_length=50, description="团队代码")
    团队描述: Optional[str] = Field(None, description="团队描述")
    团队负责人id: Optional[int] = Field(None, gt=0, description="团队负责人id")
    备注: Optional[str] = Field(None, description="备注信息")
    # 注意：最大成员数已移除，由后端根据用户会员权限自动设置


class 团队详情请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    包含成员统计: bool = Field(True, description="是否包含成员统计信息")
    包含权限信息: bool = Field(True, description="是否包含用户权限信息")


class 团队解散请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")


class 退出团队请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")


class 团队概览统计请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")


class 团队活动统计请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    天数: int = Field(30, ge=1, le=365, description="查询天数，默认30天")


class 团队活动日志请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    天数: int = Field(30, ge=1, le=365, description="查询天数，默认30天")
    操作类型: Optional[str] = Field(None, description="操作类型筛选")


# =============== 团队成员管理模型 ===============


class 团队成员加入请求(BaseModel):
    用户id: int = Field(..., gt=0, description="用户id")
    团队id: int = Field(..., gt=0, description="团队id")
    职位: Optional[str] = Field(None, max_length=100, description="职位")


class 团队成员列表请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(10, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    成员状态: Optional[str] = Field(None, description="成员状态")
    角色筛选: Optional[str] = Field(
        None, description="角色筛选：创始人、负责人、成员等"
    )


class 踢出团队成员请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    用户id: int = Field(..., gt=0, description="被踢出的用户id")
    移除原因: Optional[str] = Field(None, description="移除原因")


class 团队邀请请求(BaseModel):
    手机号: str = Field(
        ..., min_length=11, max_length=11, description="被邀请用户手机号"
    )
    团队id: int = Field(..., gt=0, description="团队id")
    角色类型: str = Field("member", description="角色类型：leader-负责人，member-成员")
    权限列表: Optional[List[str]] = Field([], description="权限代码列表")


# =============== 用户团队关系模型 ===============


class 用户团队列表请求(BaseModel):
    用户id: Optional[int] = Field(
        None, gt=0, description="用户id，不传则获取当前用户的团队"
    )
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(10, ge=1, le=100, description="每页数量")
    团队关系类型: Optional[str] = Field(
        None,
        description="团队关系类型：all-全部，created-我创建的，managed-我管理的，joined-我所在的",
    )
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    公司id: Optional[int] = Field(None, gt=0, description="公司id筛选")


class 用户团队统计请求(BaseModel):
    用户id: Optional[int] = Field(
        None, gt=0, description="用户id，不传则获取当前用户的统计"
    )


# =============== 成员权限管理模型 ===============


class 成员权限更新请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    用户id: int = Field(..., gt=0, description="要更新权限的用户id")
    角色: str = Field(..., description="成员角色")
    权限列表: List[str] = Field([], description="权限代码列表")


class 成员权限详情请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    用户id: int = Field(..., gt=0, description="要查询权限的用户id")


# =============== 团队数据看板模型 ===============


class 团队数据看板聚合请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field(
        "本周", description="时间范围：昨日、今日、本周、上周、本月、上月等"
    )
    包含模块: Optional[List[str]] = Field(
        ["overview", "performance", "business", "trends"],
        description="包含的模块列表：overview-概览，performance-绩效，business-业务，trends-趋势",
    )


class 团队业务模块请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field(
        "本周", description="时间范围：昨日、今日、本周、上周、本月、上月等"
    )


class 团队成员排名请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field("本周", description="时间范围：今日、本周、本月、本季度等")
    排序方式: str = Field(
        "好友总数",
        description="排序方式：好友总数、认领达人数、邀约总数、样品申请数、今日新增、综合绩效（与工作台指标名称一致）",
    )
    限制数量: int = Field(
        50, ge=1, le=200, description="返回的成员数量限制，默认50支持显示更多团队成员"
    )
    模块类型: Optional[str] = Field(
        None, description="模块类型：微信运营、达人管理、寄样管理。不传则返回所有数据"
    )


class 团队成员详细绩效请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: Optional[str] = Field(
        "本周", description="时间范围：昨日、今日、本周、上周、本月、上月等"
    )


class 团队达人统计请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field("本周", description="时间范围")


class 团队达人列表请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")


class 转移达人请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    达人id: int = Field(..., gt=0, description="达人id")
    原用户id: int = Field(..., gt=0, description="原用户id")
    新用户id: int = Field(..., gt=0, description="新用户id")


class 团队达人详细分析请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field("本月", description="时间范围")


class 团队达人详情请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    达人id: int = Field(..., gt=0, description="达人id")


class 团队微信达人统计请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field("本周", description="时间范围")


class 团队微信达人列表请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")


# =============== 团队管理高级功能模型 ===============


class 智能退出团队请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")


class 转移所有权请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    新所有者用户id: int = Field(..., gt=0, description="新所有者用户id")


class 成员角色更新请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    目标用户id: int = Field(..., gt=0, description="目标用户id")
    新角色: str = Field(..., description="新角色：创建者、负责人、成员")


class 用户最近活动请求(BaseModel):
    用户id: Optional[int] = Field(None, gt=0, description="用户id，不传则获取当前用户")
    限制数量: int = Field(10, ge=1, le=50, description="返回的活动记录数量")


# =============== 公司审核模型 ===============


class 公司审核请求模型(BaseModel):
    公司id: int = Field(..., gt=0, description="公司id")
    审核备注: Optional[str] = Field(None, description="审核备注")


class 公司更新请求(BaseModel):
    公司id: int = Field(..., gt=0, description="公司id")
    公司名称: Optional[str] = Field(
        None, min_length=1, max_length=255, description="公司名称"
    )
    公司简称: Optional[str] = Field(None, max_length=100, description="公司简称")
    公司代码: Optional[str] = Field(None, max_length=50, description="公司代码")
    公司地址: Optional[str] = Field(None, description="公司地址")
    联系电话: Optional[str] = Field(None, max_length=50, description="联系电话")
    邮箱: Optional[str] = Field(None, max_length=255, description="公司邮箱")
    法人代表: Optional[str] = Field(None, max_length=100, description="法人代表")
    营业执照号: Optional[str] = Field(None, max_length=100, description="营业执照号")
    备注: Optional[str] = Field(None, description="备注信息")
