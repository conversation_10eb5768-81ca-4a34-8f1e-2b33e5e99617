"""
用户微信自动化路由 - 超简化版
复用 friend-request-status 逻辑，只需要2个接口
1. 检查并处理微信添加好友（复用现有逻辑+时间控制）
2. 更新添加状态（根据id更新状态）
"""

from fastapi import APIRouter, status, Depends
from fastapi.responses import JSONResponse
from datetime import datetime

import 状态
from 依赖项.认证 import 获取当前用户
from 数据模型 import 微信模型
from 数据模型.响应模型 import 统一响应模型
from 日志 import 错误日志器
from 数据.用户微信配置数据访问层 import (
    异步创建用户微信添加配置数据访问服务,
    异步保存用户微信添加配置数据访问服务,

    异步删除用户微信配置数据访问服务,
    异步获取用户微信配置列表数据访问服务,
    异步获取用户微信配置概览统计数据访问服务,
    异步获取用户所有微信配置数据访问服务,
    异步获取用户微信账号列表数据访问服务,
    异步更新用户微信添加配置数据访问服务,
    异步获取用户微信配置数据访问服务,
    异步获取微信号配置数据访问服务,
    获取默认配置值
)

# 创建微信自动化路由器
微信自动化路由 = APIRouter()

# ================== 超简化接口 - 只需要2个 ==================

@微信自动化路由.post("/auto-add-friend", summary="微信自动添加好友", description="复用friend-request-status逻辑，增加时间控制和微信账号参数")
async def 路由接口_微信自动添加好友(
    请求数据: 微信模型.微信自动添加好友请求模型, 
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    微信自动添加好友路由接口
    
    功能说明：
    1. 完全复用 /friend-request-status 的业务逻辑
    2. 增加微信账号参数（微信信息表id）
    3. 根据用户自定义配置动态计算添加时间控制
    4. 支持个性化的时间间隔、每日次数、工作时间等限制
    
    业务逻辑：
    - 复用现有的联系方式获取逻辑
    - 获取用户为该微信号设置的个性化配置参数
    - 根据配置和历史记录动态计算下次可添加时间
    - 支持批次控制、随机延迟、周末设置等高级功能
    - 返回联系方式信息和智能时间控制信息
    
    时间控制算法：
    - 基于用户配置的每日上限、添加间隔范围
    - 考虑工作时间段、午休时间、周末设置
    - 实现批次控制（连续添加N次后长时间休息）
    - 增加随机延迟避免规律性检测
    - 模拟真人行为的成功率控制
    """
    try:
        from 服务.微信智能添加服务 import 异步执行微信智能添加分析业务服务
        
        # 调用优化后的智能添加分析服务
        智能添加分析结果 = await 异步执行微信智能添加分析业务服务(
            用户id=当前登录用户["id"],
            微信信息表id=请求数据.微信信息表id
        )
        
        # 检查业务服务层返回的状态码
        if 智能添加分析结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    智能添加分析结果.get("status", 状态.微信.更新对接进度失败),
                    智能添加分析结果.get("message", "微信智能添加分析失败")
                ).转字典()
            )
        
        # 返回智能分析结果（包含联系方式数据和时间控制信息）
        # 使用服务层返回的动态消息，而不是硬编码的"分析完成"
        return 统一响应模型.成功(
            智能添加分析结果.get("data"),
            智能添加分析结果.get("message", "分析完成")
        )
        
    except Exception as e:
        错误日志器.error(f"路由接口_微信自动添加好友异常: 用户id={当前登录用户['id']}, 微信信息表id={请求数据.微信信息表id}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "系统繁忙，请稍后重试").转字典()
        )

@微信自动化路由.post("/update-add-status", summary="更新微信添加状态", description="根据记录ID更新微信添加好友状态")
async def 路由接口_更新微信添加状态(
    请求数据: 微信模型.更新微信添加状态请求模型, 
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    更新微信添加状态路由接口
    
    功能说明：
    1. 根据记录ID更新联系方式添加记录的状态信息
    2. 记录实际添加时间，重新计算时间控制参数
    3. 基于实际操作时间动态调整计划执行时间
    4. 更新计数器和休息状态
    
    核心业务逻辑：
    - 验证记录归属权限和数据有效性
    - 记录用户实际执行添加操作的准确时间
    - 基于实际时间和用户配置重新计算时间控制
    - 更新当日计数、连续操作计数等统计信息
    - 判断是否需要触发长时间休息机制
    
    智能时间重计算：
    - 实际添加时间作为新的基准时间点
    - 根据用户配置动态计算下次计划执行时间
    - 考虑工作时间限制自动调整到有效时间段
    - 处理跨日期的计数重置逻辑
    """
    try:
        from 服务.微信智能添加服务 import 异步处理微信添加状态更新业务服务
        
        # 调用优化后的智能状态更新服务
        状态更新处理结果 = await 异步处理微信添加状态更新业务服务(
            用户id=当前登录用户["id"],
            记录id=请求数据.记录id,
            新状态=请求数据.好友请求状态,
            备注信息=请求数据.备注,
            实际操作时间=datetime.now()  # 记录用户实际执行操作的时间
        )
        
        # 检查业务服务层返回的状态码
        if 状态更新处理结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    状态更新处理结果.get("status", 状态.微信.更新对接进度失败),
                    状态更新处理结果.get("message", "状态更新失败，请稍后重试")
                ).转字典()
            )
        
        # 返回处理结果（包含重新计算的时间控制信息）
        return 统一响应模型.成功(状态更新处理结果.get("data"), "状态更新成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_更新微信添加状态异常: 用户id={当前登录用户['id']}, 记录id={请求数据.记录id}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "状态更新失败，请稍后重试").转字典()
        )

# ================== 用户微信配置管理接口 ==================

@微信自动化路由.post("/config", summary="保存微信添加配置", description="为指定微信号保存个性化添加参数配置")
async def 路由接口_保存微信添加配置(
    请求数据: 微信模型.用户微信添加配置请求模型, 
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    保存微信添加配置路由接口
    
    功能说明：
    1. 为用户的指定微信号保存个性化添加参数
    2. 支持全面的时间控制、批次管理、随机化等配置
    3. 如果配置已存在则更新，不存在则创建
    
    配置参数包括：
    - 基础限制：每日次数上限、添加间隔时间范围
    - 时间管理：工作时间段、午休时间、周末设置
    - 批次控制：连续添加上限、批次间休息时间
    - 随机化：随机延迟范围、成功率模拟
    - 安全防护：每小时限制、异常暂停时间
    """
    try:
        # 调用数据访问层保存配置
        保存结果 = await 异步保存用户微信添加配置数据访问服务(
            用户id=当前登录用户["id"],
            配置数据=请求数据.model_dump()
        )
        
        # 检查数据访问层返回的状态码
        if 保存结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    保存结果.get("status", 状态.微信.更新对接进度失败),
                    保存结果.get("message", "保存微信添加配置失败")
                ).转字典()
            )
        
        # 返回保存结果
        return 统一响应模型.成功(保存结果.get("data"), "配置保存成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_保存微信添加配置异常: 用户id={当前登录用户['id']}, 微信信息表id={请求数据.微信信息表id}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "配置保存失败，请稍后重试").转字典()
        )

@微信自动化路由.get("/config/{微信信息表id}", summary="获取微信添加配置", description="获取指定微信号的添加参数配置")
async def 路由接口_获取微信添加配置(
    微信信息表id: int,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    获取微信添加配置路由接口
    
    功能说明：
    1. 获取用户指定微信号的个性化添加参数配置
    2. 如果用户未设置配置，返回系统默认配置
    3. 配置用于计算下次添加时间和判断是否可以添加
    
    返回信息：
    - 完整的配置参数
    - 配置状态和更新时间
    - 如果没有自定义配置，返回默认值
    """
    try:
        # 调用数据访问层获取配置
        配置结果 = await 异步获取微信号配置数据访问服务(
            用户id=当前登录用户["id"],
            微信信息表id=微信信息表id
        )
        
        # 检查数据访问层返回的状态码
        if 配置结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    配置结果.get("status", 状态.微信.获取对接进度列表失败),
                    配置结果.get("message", "配置获取失败，请稍后重试")
                ).转字典()
            )
        
        # 返回配置结果
        return 统一响应模型.成功(配置结果.get("data"), "配置获取成功")

    except Exception as e:
        错误日志器.error(f"路由接口_获取微信添加配置异常: 用户id={当前登录用户['id']}, 微信信息表id={微信信息表id}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "配置获取失败，请稍后重试").转字典()
        )


@微信自动化路由.post("/config/detail", summary="获取微信配置详情", description="根据配置id获取完整的微信自动化配置详情")
async def 路由接口_获取微信配置详情(
    请求数据: 微信模型.获取配置详情请求模型,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    获取微信配置详情路由接口

    功能说明：
    1. 根据配置id获取完整的配置详情信息
    2. 验证配置归属权限
    3. 返回所有配置参数和状态信息

    返回信息：
    - 完整的配置参数详情
    - 配置的创建和更新时间
    - 绑定的微信账号信息
    """
    try:
        # 调用数据访问层获取配置详情
        配置详情结果 = await 异步获取用户微信配置数据访问服务(
            用户id=当前登录用户["id"],
            配置id=请求数据.配置id
        )

        # 检查数据访问层返回的状态码
        if 配置详情结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    配置详情结果.get("status", 状态.微信.获取对接进度列表失败),
                    配置详情结果.get("message", "配置详情获取失败，请稍后重试")
                ).转字典()
            )

        # 返回配置详情结果
        return 统一响应模型.成功(配置详情结果.get("data"), "配置详情获取成功")

    except Exception as e:
        错误日志器.error(f"路由接口_获取微信配置详情异常: 用户id={当前登录用户['id']}, 配置id={请求数据.配置id}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "系统繁忙，请稍后重试").转字典()
        )

@微信自动化路由.post("/overview", summary="获取配置概览统计", description="获取配置总数、启用数量、绑定账号数等统计信息")
async def 路由接口_获取配置概览统计(
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    获取配置概览统计路由接口
    
    功能说明：
    1. 获取配置总数、启用配置数、绑定微信账号数等统计信息
    2. 获取今日添加好友数等关键指标
    3. 用于管理页面的概览卡片展示
    
    返回信息：
    - 配置总数、启用配置数
    - 绑定微信账号数
    - 今日添加好友数
    """
    try:
        # 调用数据访问层获取概览统计
        概览统计结果 = await 异步获取用户微信配置概览统计数据访问服务(
            用户id=当前登录用户["id"]
        )
        
        # 检查数据访问层返回的状态码
        if 概览统计结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    概览统计结果.get("status", 状态.微信.获取对接进度列表失败),
                    概览统计结果.get("message", "概览统计获取失败，请稍后重试")
                ).转字典()
            )
        
        # 返回概览统计结果
        return 统一响应模型.成功(概览统计结果.get("data"), "概览统计获取成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_获取配置概览统计异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "概览统计获取失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/accounts", summary="获取微信账号列表", description="获取用户可用的微信账号列表，用于配置绑定")
async def 路由接口_获取微信账号列表(
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    获取微信账号列表路由接口
    
    功能说明：
    1. 获取当前用户可用的微信账号列表
    2. 用于配置表单中的微信账号选择下拉框
    3. 返回微信号、备注等基本信息
    
    返回信息：
    - 微信账号id、微信号、备注信息
    - 账号状态和创建时间
    """
    try:
        # 调用数据访问层获取微信账号列表
        账号列表结果 = await 异步获取用户微信账号列表数据访问服务(
            用户id=当前登录用户["id"]
        )
        
        # 检查数据访问层返回的状态码
        if 账号列表结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    账号列表结果.get("status", 状态.微信.获取对接进度列表失败),
                    账号列表结果.get("message", "微信账号列表获取失败，请稍后重试")
                ).转字典()
            )
        
        # 返回微信账号列表结果
        return 统一响应模型.成功(账号列表结果.get("data"), "微信账号列表获取成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_获取微信账号列表异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "微信账号列表获取失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/list", summary="获取配置列表", description="获取用户微信自动化配置列表，支持分页和搜索")
async def 路由接口_获取配置列表(
    请求数据: 微信模型.配置列表查询请求模型,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    获取配置列表路由接口
    
    功能说明：
    1. 获取用户微信自动化配置列表
    2. 支持分页查询和搜索功能
    3. 返回配置的详细信息和状态
    
    返回信息：
    - 分页的配置列表数据
    - 配置总数和分页信息
    - 每个配置的详细参数
    """
    try:
        # 调用数据访问层获取配置列表
        配置列表结果 = await 异步获取用户微信配置列表数据访问服务(
            用户id=当前登录用户["id"],
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            搜索关键词=请求数据.搜索关键词
        )
        
        # 检查数据访问层返回的状态码
        if 配置列表结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    配置列表结果.get("status", 状态.微信.获取对接进度列表失败),
                    配置列表结果.get("message", "配置列表获取失败，请稍后重试")
                ).转字典()
            )
        
        # 返回配置列表结果
        return 统一响应模型.成功(配置列表结果.get("data"), "配置列表获取成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_获取配置列表异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "配置列表获取失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/create", summary="创建微信自动化配置", description="创建新的微信自动化配置")
async def 路由接口_创建微信自动化配置(
    请求数据: 微信模型.用户微信添加配置请求模型, 
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    创建微信自动化配置路由接口
    
    功能说明：
    1. 创建新的微信自动化添加配置
    2. 验证配置参数的合法性
    3. 保存完整的配置信息到数据库
    
    返回信息：
    - 新创建的配置id和基本信息
    - 创建结果状态
    """
    try:
        # 调用数据访问层创建配置
        创建结果 = await 异步创建用户微信添加配置数据访问服务(
            用户id=当前登录用户["id"],
            配置数据=请求数据.model_dump()
        )
        
        # 检查数据访问层返回的状态码
        if 创建结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    创建结果.get("status", 状态.微信.更新对接进度失败),
                    创建结果.get("message", "配置创建失败，请稍后重试")
                ).转字典()
            )
        
        # 返回创建结果
        return 统一响应模型.成功(创建结果.get("data"), "配置创建成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_创建微信自动化配置异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "配置创建失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/update", summary="更新微信自动化配置", description="更新现有的微信自动化配置")
async def 路由接口_更新微信自动化配置(
    请求数据: 微信模型.更新微信自动化配置请求模型, 
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    更新微信自动化配置路由接口
    
    功能说明：
    1. 更新现有的微信自动化添加配置
    2. 验证配置id的归属权限
    3. 更新配置参数到数据库
    
    返回信息：
    - 更新结果状态
    - 更新后的配置信息
    """
    try:
        # 调用数据访问层更新配置
        更新结果 = await 异步更新用户微信添加配置数据访问服务(
            用户id=当前登录用户["id"],
            配置id=请求数据.配置id,
            配置数据=请求数据.model_dump(exclude={'配置id'})
        )
        
        # 检查数据访问层返回的状态码
        if 更新结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    更新结果.get("status", 状态.微信.更新对接进度失败),
                    更新结果.get("message", "配置更新失败，请稍后重试")
                ).转字典()
            )
        
        # 返回更新结果
        return 统一响应模型.成功(更新结果.get("data"), "配置更新成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_更新微信自动化配置异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "配置更新失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/delete", summary="删除微信自动化配置", description="删除指定的微信自动化配置")
async def 路由接口_删除微信自动化配置(
    请求数据: 微信模型.删除微信自动化配置请求模型, 
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    删除微信自动化配置路由接口
    
    功能说明：
    1. 删除指定的微信自动化添加配置
    2. 验证配置id的归属权限
    3. 执行软删除操作（禁用配置）
    
    返回信息：
    - 删除结果状态
    - 被删除的配置信息
    """
    try:
        # 调用数据访问层删除配置
        删除结果 = await 异步删除用户微信配置数据访问服务(
            用户id=当前登录用户["id"],
            配置id=请求数据.配置id
        )
        
        # 检查数据访问层返回的状态码
        if 删除结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    删除结果.get("status", 状态.微信.更新对接进度失败),
                    删除结果.get("message", "配置删除失败，请稍后重试")
                ).转字典()
            )
        
        # 返回删除结果
        return 统一响应模型.成功(删除结果.get("data"), "配置删除成功")

    except Exception as e:
        错误日志器.error(f"路由接口_删除微信自动化配置异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "配置删除失败，请稍后重试").转字典()
        )


@微信自动化路由.get("/default-config", summary="获取默认配置", description="获取系统默认配置值")
async def 路由接口_获取默认配置():
    """
    获取默认配置路由接口

    功能说明：
    1. 获取系统默认配置值
    2. 用于前端表单初始化
    3. 支持动态配置管理

    返回信息：
    - 所有默认配置项及其值
    """
    try:
        # 获取默认配置
        默认配置 = await 获取默认配置值()

        return 统一响应模型.成功(默认配置, "默认配置获取成功")

    except Exception as e:
        错误日志器.error(f"路由接口_获取默认配置异常: 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.失败, "默认配置获取失败，请稍后重试").转字典()
        )


@微信自动化路由.get("/config-list", summary="获取用户所有微信配置", description="获取当前用户所有微信号的添加配置列表")
async def 路由接口_获取用户所有微信配置(
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    获取用户所有微信配置路由接口
    
    功能说明：
    1. 获取当前用户名下所有微信号的添加配置
    2. 显示每个微信号的配置状态和主要参数
    3. 用于配置管理页面展示
    
    返回信息：
    - 微信号列表及其配置状态
    - 主要配置参数概览
    - 配置的启用状态
    """
    try:
        # 调用数据访问层获取所有配置
        配置列表结果 = await 异步获取用户所有微信配置数据访问服务(
            用户id=当前登录用户["id"]
        )
        
        # 检查数据访问层返回的状态码
        if 配置列表结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    配置列表结果.get("status", 状态.微信.获取对接进度列表失败),
                    配置列表结果.get("message", "配置列表获取失败，请稍后重试")
                ).转字典()
            )
        
        # 返回配置列表结果
        return 统一响应模型.成功(配置列表结果.get("data"), "配置列表获取成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_获取用户所有微信配置异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "配置列表获取失败，请稍后重试").转字典()
        )

# ================== 自动添加记录管理接口 ==================

@微信自动化路由.post("/records-list", summary="获取添加记录列表", description="获取用户微信自动添加好友记录列表，支持分页和搜索")
async def 路由接口_获取添加记录列表(
    请求数据: 微信模型.添加记录列表查询请求模型,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    获取添加记录列表路由接口
    
    功能说明：
    1. 获取用户微信自动添加好友的所有记录
    2. 支持分页查询、关键词搜索和状态筛选
    3. 返回记录详情、添加状态、时间信息等
    
    返回信息：
    - 分页的添加记录列表
    - 每条记录的详细状态和时间信息
    - 关联的微信账号和达人信息
    """
    try:
        from 数据.微信添加记录数据访问层 import 异步获取用户添加记录列表数据访问服务
        
        # 调用数据访问层获取记录列表
        记录列表结果 = await 异步获取用户添加记录列表数据访问服务(
            用户id=当前登录用户["id"],
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            搜索关键词=请求数据.搜索关键词,
            状态筛选=请求数据.状态筛选,
            微信账号筛选=请求数据.微信账号筛选,
            时间范围=请求数据.时间范围
        )
        
        # 检查数据访问层返回的状态码
        if 记录列表结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    记录列表结果.get("status", 状态.微信.获取对接进度列表失败),
                    记录列表结果.get("message", "记录列表获取失败，请稍后重试")
                ).转字典()
            )
        
        # 返回记录列表结果
        return 统一响应模型.成功(记录列表结果.get("data"), "记录列表获取成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_获取添加记录列表异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "记录列表获取失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/manual-add", summary="手动添加记录", description="手动创建微信添加好友记录")
async def 路由接口_手动添加记录(
    请求数据: 微信模型.手动添加记录请求模型,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    手动添加记录路由接口
    
    功能说明：
    1. 手动创建微信添加好友记录
    2. 支持设置计划添加时间和验证消息
    3. 自动关联到指定的微信账号
    
    返回信息：
    - 创建的记录ID和基本信息
    - 创建结果状态
    """
    try:
        from 数据.微信添加记录数据访问层 import 异步创建手动添加记录数据访问服务
        
        # 调用数据访问层创建记录
        创建结果 = await 异步创建手动添加记录数据访问服务(
            用户id=当前登录用户["id"],
            记录数据=请求数据.model_dump()
        )
        
        # 检查数据访问层返回的状态码
        if 创建结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    创建结果.get("status", 状态.微信.更新对接进度失败),
                    创建结果.get("message", "记录创建失败，请稍后重试")
                ).转字典()
            )
        
        # 返回创建结果
        return 统一响应模型.成功(创建结果.get("data"), "记录创建成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_手动添加记录异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "记录创建失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/delete", summary="删除添加记录", description="删除单个添加记录")
async def 路由接口_删除添加记录(
    请求数据: 微信模型.记录ID请求模型,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    删除添加记录路由接口
    
    功能说明：
    1. 删除单个添加记录
    2. 验证记录归属权限
    3. 执行软删除或硬删除
    
    返回信息：
    - 删除结果状态
    - 被删除的记录信息
    """
    try:
        from 数据.微信添加记录数据访问层 import 异步删除添加记录数据访问服务
        
        # 调用数据访问层删除记录
        删除结果 = await 异步删除添加记录数据访问服务(
            用户id=当前登录用户["id"],
            记录ID=请求数据.记录ID
        )
        
        # 检查数据访问层返回的状态码
        if 删除结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    删除结果.get("status", 状态.微信.更新对接进度失败),
                    删除结果.get("message", "记录删除失败，请稍后重试")
                ).转字典()
            )
        
        # 返回删除结果
        return 统一响应模型.成功(删除结果.get("data"), "记录删除成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_删除添加记录异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "记录删除失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/batch-delete", summary="批量删除记录", description="批量删除多个添加记录")
async def 路由接口_批量删除记录(
    请求数据: 微信模型.批量记录IDs请求模型,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    批量删除记录路由接口
    
    功能说明：
    1. 批量删除多个添加记录
    2. 批量验证记录归属权限
    3. 执行批量软删除或硬删除
    
    返回信息：
    - 批量删除结果统计
    - 成功和失败的记录数量
    """
    try:
        from 数据.微信添加记录数据访问层 import 异步批量删除添加记录数据访问服务
        
        # 调用数据访问层批量删除记录
        批量删除结果 = await 异步批量删除添加记录数据访问服务(
            用户id=当前登录用户["id"],
            记录IDs=请求数据.记录IDs
        )
        
        # 检查数据访问层返回的状态码
        if 批量删除结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    批量删除结果.get("status", 状态.微信.更新对接进度失败),
                    批量删除结果.get("message", "批量删除失败，请稍后重试")
                ).转字典()
            )
        
        # 返回批量删除结果
        return 统一响应模型.成功(批量删除结果.get("data"), "批量删除成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_批量删除记录异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "批量删除失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/export-records", summary="导出添加记录", description="导出添加记录到Excel文件")
async def 路由接口_导出添加记录(
    请求数据: 微信模型.导出记录请求模型,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    导出添加记录路由接口
    
    功能说明：
    1. 导出用户的添加记录到Excel文件
    2. 支持根据筛选条件导出
    3. 包含完整的记录信息和统计数据
    
    返回信息：
    - Excel文件的二进制数据
    - 文件下载信息
    """
    try:
        from 数据.微信添加记录数据访问层 import 异步导出添加记录数据访问服务
        
        # 调用数据访问层导出记录
        导出结果 = await 异步导出添加记录数据访问服务(
            用户id=当前登录用户["id"],
            导出参数=请求数据.model_dump()
        )
        
        # 检查数据访问层返回的状态码
        if 导出结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    导出结果.get("status", 状态.微信.获取对接进度列表失败),
                    导出结果.get("message", "记录导出失败，请稍后重试")
                ).转字典()
            )
        
        # 返回文件数据
        from fastapi.responses import StreamingResponse
        import io
        
        文件数据 = 导出结果.get("data", {}).get("文件数据")
        文件名 = 导出结果.get("data", {}).get("文件名", "添加记录.xlsx")
        
        return StreamingResponse(
            io.BytesIO(文件数据),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={文件名}"}
        )
        
    except Exception as e:
        错误日志器.error(f"路由接口_导出添加记录异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.更新对接进度失败, "记录导出失败，请稍后重试").转字典()
        )

@微信自动化路由.post("/record-detail", summary="获取记录详情", description="获取单个添加记录的详细信息")
async def 路由接口_获取记录详情(
    请求数据: 微信模型.记录ID请求模型,
    当前登录用户: dict = Depends(获取当前用户)
):
    """
    获取记录详情路由接口
    
    功能说明：
    1. 获取单个添加记录的详细信息
    2. 验证记录归属权限
    3. 返回完整的记录数据和关联信息
    
    返回信息：
    - 完整的记录详情
    - 关联的微信账号和达人信息
    - 添加历史和状态变更记录
    """
    try:
        from 数据.微信添加记录数据访问层 import 异步获取添加记录详情数据访问服务
        
        # 调用数据访问层获取记录详情
        详情结果 = await 异步获取添加记录详情数据访问服务(
            用户id=当前登录用户["id"],
            记录ID=请求数据.记录ID
        )
        
        # 检查数据访问层返回的状态码
        if 详情结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    详情结果.get("status", 状态.微信.获取对接进度列表失败),
                    详情结果.get("message", "记录详情获取失败，请稍后重试")
                ).转字典()
            )
        
        # 返回记录详情
        return 统一响应模型.成功(详情结果.get("data"), "记录详情获取成功")
        
    except Exception as e:
        错误日志器.error(f"路由接口_获取记录详情异常: 用户id={当前登录用户['id']}, 错误={str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.微信.获取对接进度列表失败, "记录详情获取失败，请稍后重试").转字典()
        )
