"""
Lang<PERSON><PERSON>n JSON Schema验证器
统一的JSON Schema格式验证工具，避免代码重复
"""

from typing import Any, Dict

from 日志记录器 import 获取日志记录器

# 创建日志记录器
schema_validator_logger = 获取日志记录器("LangChain.JSONSchemaValidator")


class JSONSchemaValidator:
    """JSON Schema验证器 - 统一验证逻辑"""

    @staticmethod
    def validate_field_type_consistency(
        field_name: str, field_def: Dict[str, Any], logger=None
    ) -> bool:
        """
        验证字段类型一致性，检测常见的格式冲突

        Args:
            field_name: 字段名称
            field_def: 字段定义
            logger: 可选的日志记录器，如果不提供则使用默认的

        Returns:
            bool: 验证是否通过
        """
        if logger is None:
            logger = schema_validator_logger

        try:
            if not isinstance(field_def, dict):
                logger.error(f"❌ 字段 {field_name} 定义必须是字典格式")
                return False

            if "type" not in field_def:
                logger.error(f"❌ 字段 {field_name} 缺少type定义")
                return False

            field_type = field_def.get("type")

            # 验证基本类型
            valid_types = ["string", "integer", "number", "boolean", "array", "object"]
            if field_type not in valid_types:
                logger.error(f"❌ 字段 {field_name} 的类型 {field_type} 不支持")
                return False

            # 检测类型冲突 - 精简版本
            conflicts = {
                "string": ["items", "properties"],
                "array": ["enum"],
                "object": ["items"],
                "integer": ["items", "properties"],
                "number": ["items", "properties"],
                "boolean": ["items", "properties", "enum"],
            }

            if field_type in conflicts:
                for conflict_attr in conflicts[field_type]:
                    if conflict_attr in field_def:
                        logger.error(
                            f"❌ 字段 {field_name} ({field_type}类型) 不应该有{conflict_attr}属性"
                        )
                        return False

            # 特殊检查：string类型的default值
            if (
                field_type == "string"
                and "default" in field_def
                and not isinstance(field_def["default"], (str, type(None)))
                and not (
                    isinstance(field_def["default"], list)
                    and len(field_def["default"]) == 0
                )
            ):
                logger.error(f"❌ 字段 {field_name} (string类型) default值类型不正确")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ 验证字段 {field_name} 类型一致性失败: {str(e)}")
            return False

    @staticmethod
    def validate_json_schema_format(json_schema: Dict[str, Any], logger=None) -> bool:
        """
        验证JSON Schema基本格式

        Args:
            json_schema: JSON Schema定义
            logger: 可选的日志记录器

        Returns:
            bool: 验证是否通过
        """
        if logger is None:
            logger = schema_validator_logger

        try:
            # 基本格式检查
            if not isinstance(json_schema, dict):
                logger.error("❌ JSON Schema必须是字典格式")
                return False

            if "properties" not in json_schema:
                logger.error("❌ JSON Schema缺少properties字段")
                return False

            properties = json_schema.get("properties", {})
            if not isinstance(properties, dict):
                logger.error("❌ properties字段必须是字典格式")
                return False

            if len(properties) == 0:
                logger.warning("⚠️ JSON Schema没有定义任何属性")
                return False

            # 验证每个字段的类型一致性
            for field_name, field_def in properties.items():
                if not JSONSchemaValidator.validate_field_type_consistency(
                    field_name, field_def, logger
                ):
                    return False

            return True

        except Exception as e:
            logger.error(f"❌ JSON Schema格式验证异常: {str(e)}")
            return False


# 创建全局实例
json_schema_validator = JSONSchemaValidator()
